{"name": "增强格式Web测试序列", "description": "使用增强自动化管理器的ActionType + LogicType格式", "version": "2.0", "format": "enhanced", "steps": [{"StepOrder": 1, "ActionType": "web_goto", "LogicType": null, "Description": "访问百度首页", "Parameters": {"url": "https://www.baidu.com"}, "TimeoutSeconds": 10, "MaxRetries": 3, "IsActive": true, "LoopCount": 1, "LoopVariable": null, "GroupId": "navigation"}, {"StepOrder": 2, "ActionType": "web_screenshot", "LogicType": null, "Description": "截取百度首页截图", "Parameters": {"filename": "baidu_homepage.png"}, "TimeoutSeconds": 5, "MaxRetries": 2, "IsActive": true, "LoopCount": 1, "LoopVariable": null, "GroupId": "capture"}, {"StepOrder": 3, "ActionType": "web_fill", "LogicType": null, "Description": "在搜索框中输入关键词", "Parameters": {"selector": "#kw", "text": "Playwright自动化测试"}, "TimeoutSeconds": 5, "MaxRetries": 3, "IsActive": true, "LoopCount": 1, "LoopVariable": null, "GroupId": "search"}, {"StepOrder": 4, "ActionType": "web_click", "LogicType": null, "Description": "点击搜索按钮", "Parameters": {"selector": "#su"}, "TimeoutSeconds": 5, "MaxRetries": 3, "IsActive": true, "LoopCount": 1, "LoopVariable": null, "GroupId": "search"}, {"StepOrder": 5, "ActionType": "web_wait", "LogicType": null, "Description": "等待搜索结果加载", "Parameters": {"timeout": 3000}, "TimeoutSeconds": 5, "MaxRetries": 1, "IsActive": true, "LoopCount": 1, "LoopVariable": null, "GroupId": "search"}, {"StepOrder": 6, "ActionType": "web_screenshot", "LogicType": null, "Description": "截取搜索结果页面", "Parameters": {"filename": "search_results.png"}, "TimeoutSeconds": 5, "MaxRetries": 2, "IsActive": true, "LoopCount": 1, "LoopVariable": null, "GroupId": "capture"}], "metadata": {"created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "expected_duration": 25, "browser_requirements": ["chromium"], "environment": "development", "format_version": "2.0"}}