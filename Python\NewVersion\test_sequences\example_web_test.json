{"name": "示例Web自动化测试", "description": "演示Playwright Web自动化测试的基本功能", "version": "1.0", "author": "自动化测试系统", "tags": ["demo", "web", "basic"], "steps": [{"action": "goto", "url": "https://httpbin.org/", "description": "访问httpbin测试网站首页", "timeout": 10000}, {"action": "screenshot", "filename": "httpbin_homepage.png", "description": "截取首页截图"}, {"action": "assert_text", "selector": "h1", "text": "httpbin", "description": "验证页面标题包含httpbin"}, {"action": "wait", "timeout": 2000, "description": "等待2秒"}, {"action": "goto", "url": "https://httpbin.org/forms/post", "description": "访问表单测试页面"}, {"action": "fill", "selector": "input[name='custname']", "text": "自动化测试用户", "description": "填写客户姓名字段"}, {"action": "fill", "selector": "input[name='custtel']", "text": "13800138000", "description": "填写电话号码字段"}, {"action": "fill", "selector": "input[name='custemail']", "text": "<EMAIL>", "description": "填写邮箱地址字段"}, {"action": "fill", "selector": "textarea[name='custmsg']", "text": "这是一个自动化测试消息", "description": "填写消息内容"}, {"action": "screenshot", "filename": "form_filled.png", "description": "截取填写完成的表单"}, {"action": "click", "selector": "input[type='submit']", "description": "点击提交按钮"}, {"action": "wait", "timeout": 3000, "description": "等待页面加载"}, {"action": "screenshot", "filename": "form_submitted.png", "description": "截取提交后的页面"}, {"action": "assert_text", "selector": "body", "text": "自动化测试用户", "description": "验证提交的数据出现在响应中"}], "metadata": {"created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z", "expected_duration": 30, "browser_requirements": ["chromium", "firefox", "webkit"], "environment": "development"}}