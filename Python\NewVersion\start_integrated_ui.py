#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动集成了Playwright的主界面
"""

import sys
import os
from pathlib import Path

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    missing_deps = []
    
    # 检查基础依赖
    try:
        import tkinter
        print("✅ tkinter 可用")
    except ImportError:
        missing_deps.append("tkinter")
    
    try:
        import requests
        print("✅ requests 可用")
    except ImportError:
        missing_deps.append("requests")
    
    # 检查Playwright
    try:
        import playwright
        print("✅ playwright 可用")
        
        # 检查浏览器
        try:
            from playwright.sync_api import sync_playwright
            with sync_playwright() as p:
                # 尝试启动chromium
                browser = p.chromium.launch(headless=True)
                browser.close()
            print("✅ Playwright浏览器可用")
        except Exception as e:
            print(f"⚠️ Playwright浏览器不可用: {e}")
            print("💡 请运行: playwright install")
            
    except ImportError:
        missing_deps.append("playwright")
        print("⚠️ playwright 不可用")
    
    if missing_deps:
        print(f"\n❌ 缺少依赖: {', '.join(missing_deps)}")
        print("请运行以下命令安装:")
        for dep in missing_deps:
            if dep == "playwright":
                print(f"  pip install {dep}")
                print("  playwright install")
            else:
                print(f"  pip install {dep}")
        return False
    
    print("✅ 所有依赖检查通过")
    return True

def start_application():
    """启动应用"""
    print("\n🚀 启动集成UI...")
    
    try:
        # 导入主应用
        from main import MainApplication
        
        # 创建并运行应用
        app = MainApplication()
        
        print("✅ 应用启动成功")
        print("\n💡 使用提示:")
        print("   1. 切换到 '🌐 Web测试' 选项卡使用Playwright功能")
        print("   2. 首次使用需要点击'启动浏览器'")
        print("   3. 可以加载示例测试序列或编写自己的测试")
        print("   4. 支持Chromium、Firefox、WebKit三种浏览器")
        
        # 运行主循环
        app.root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保在正确的目录中运行此脚本")
        return False
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def show_help():
    """显示帮助信息"""
    help_text = """
🌐 Playwright Web自动化测试集成

功能特性:
✅ 多浏览器支持 (Chromium, Firefox, WebKit)
✅ 图形化测试界面
✅ 丰富的测试动作 (导航、点击、填写、截图等)
✅ 测试序列管理
✅ 详细的测试报告
✅ 与现有自动化系统集成

使用步骤:
1. 确保已安装依赖: pip install playwright
2. 安装浏览器: playwright install
3. 运行此脚本启动界面
4. 切换到"Web测试"选项卡
5. 启动浏览器并执行测试

测试序列格式:
{
  "name": "测试名称",
  "steps": [
    {"action": "goto", "url": "https://example.com"},
    {"action": "click", "selector": "#button"},
    {"action": "screenshot", "filename": "result.png"}
  ]
}

支持的动作:
- goto: 导航到URL
- click: 点击元素
- fill: 填写输入框
- screenshot: 截图
- wait: 等待
- assert_text: 断言文本

更多信息请查看 README_Playwright.md
"""
    print(help_text)

def main():
    """主函数"""
    print("=" * 60)
    print("🌐 Playwright Web自动化测试 - 集成UI启动器")
    print("=" * 60)
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help', 'help']:
            show_help()
            return
        elif sys.argv[1] in ['--check', 'check']:
            check_dependencies()
            return
    
    # 切换到脚本目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装必要的依赖")
        return
    
    # 启动应用
    success = start_application()
    
    if success:
        print("\n👋 应用已关闭")
    else:
        print("\n❌ 应用启动失败")

if __name__ == "__main__":
    main()
