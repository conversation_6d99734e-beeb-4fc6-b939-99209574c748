#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI集成测试脚本
测试Playwright功能是否正确集成到主界面
"""

import tkinter as tk
from tkinter import ttk
import sys
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_playwright_ui_integration():
    """测试Playwright UI集成"""
    print("🧪 开始测试Playwright UI集成...")
    
    try:
        # 导入必要的模块
        from playwright_test_ui import PlaywrightTestUI, PLAYWRIGHT_AVAILABLE
        
        print(f"✅ Playwright可用性: {PLAYWRIGHT_AVAILABLE}")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("Playwright UI集成测试")
        root.geometry("800x600")
        
        # 创建选项卡容器
        notebook = ttk.Notebook(root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建Playwright测试选项卡
        playwright_frame = ttk.Frame(notebook)
        notebook.add(playwright_frame, text="🌐 Web测试")
        
        # 初始化Playwright UI
        playwright_ui = PlaywrightTestUI(parent=playwright_frame, automation_manager=None)
        
        print("✅ Playwright UI创建成功")
        print(f"   嵌入模式: {playwright_ui.is_embedded}")
        
        # 添加测试信息标签
        info_frame = ttk.Frame(notebook)
        notebook.add(info_frame, text="ℹ️ 测试信息")
        
        info_text = tk.Text(info_frame, wrap=tk.WORD, padx=10, pady=10)
        info_text.pack(fill=tk.BOTH, expand=True)
        
        test_info = f"""
Playwright UI集成测试

✅ 测试状态: 成功
✅ Playwright可用: {PLAYWRIGHT_AVAILABLE}
✅ UI嵌入模式: {playwright_ui.is_embedded}

测试说明:
1. 切换到"Web测试"选项卡查看Playwright界面
2. 如果Playwright已安装，可以启动浏览器进行测试
3. 界面应该正确适配选项卡容器

安装Playwright:
pip install playwright
playwright install

使用方法:
1. 选择浏览器类型
2. 点击"启动浏览器"
3. 编辑或加载测试序列
4. 点击"执行测试"
"""
        
        info_text.insert(tk.END, test_info)
        info_text.config(state=tk.DISABLED)
        
        # 显示窗口
        root.mainloop()
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_ui_integration():
    """测试主UI集成"""
    print("🧪 开始测试主UI集成...")
    
    try:
        # 导入主应用
        from main import MainApplication
        
        print("✅ 主应用导入成功")
        
        # 创建应用实例（不运行mainloop）
        app = MainApplication()
        
        # 检查是否有Playwright选项卡
        if hasattr(app, 'notebook'):
            tab_count = app.notebook.index("end")
            print(f"✅ 选项卡数量: {tab_count}")
            
            # 检查选项卡标题
            for i in range(tab_count):
                tab_text = app.notebook.tab(i, "text")
                print(f"   选项卡 {i}: {tab_text}")
                if "Web测试" in tab_text:
                    print("✅ 找到Web测试选项卡")
        
        # 检查是否有playwright_ui属性
        if hasattr(app, 'playwright_ui'):
            print(f"✅ Playwright UI已集成: {app.playwright_ui is not None}")
        else:
            print("⚠️ 未找到playwright_ui属性")
        
        # 关闭应用
        app.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 主UI集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("🚀 Playwright UI集成测试")
    print("=" * 50)
    
    # 测试1: Playwright UI组件
    test1_result = test_playwright_ui_integration()
    
    print("\n" + "=" * 50)
    
    # 测试2: 主UI集成（注释掉，因为会启动完整应用）
    # test2_result = test_main_ui_integration()
    
    print("\n📊 测试结果:")
    print(f"   Playwright UI组件: {'✅ 通过' if test1_result else '❌ 失败'}")
    # print(f"   主UI集成: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result:
        print("\n🎉 集成测试成功！")
        print("💡 提示: 现在可以运行 python main.py 查看完整的集成效果")
    else:
        print("\n❌ 集成测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
