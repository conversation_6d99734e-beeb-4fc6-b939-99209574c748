{"name": "错误监控演示测试", "description": "演示Playwright错误监控功能，包括控制台错误、页面错误和网络错误", "steps": [{"action": "goto", "url": "data:text/html,<html><head><title>错误监控测试</title></head><body><h1>错误监控演示页面</h1><button id='console-error' onclick='console.error(\"这是一个控制台错误\")'>触发控制台错误</button><button id='console-warn' onclick='console.warn(\"这是一个控制台警告\")'>触发控制台警告</button><button id='js-error' onclick='nonExistentFunction()'>触发JS错误</button><button id='network-error' onclick='fetch(\"/nonexistent-api\")'>触发网络错误</button><img src='/nonexistent-image.jpg' alt='不存在的图片' style='display:none;'></body></html>", "description": "加载包含各种错误触发器的测试页面"}, {"action": "screenshot", "filename": "error_demo_page.png", "description": "截取测试页面"}, {"action": "wait", "timeout": 1000, "description": "等待页面加载完成"}, {"action": "click", "selector": "#console-error", "description": "点击按钮触发控制台错误"}, {"action": "wait", "timeout": 500, "description": "等待错误记录"}, {"action": "click", "selector": "#console-warn", "description": "点击按钮触发控制台警告"}, {"action": "wait", "timeout": 500, "description": "等待警告记录"}, {"action": "click", "selector": "#js-error", "description": "点击按钮触发JavaScript错误"}, {"action": "wait", "timeout": 500, "description": "等待JS错误记录"}, {"action": "click", "selector": "#network-error", "description": "点击按钮触发网络错误"}, {"action": "wait", "timeout": 1000, "description": "等待网络错误记录"}, {"action": "screenshot", "filename": "after_errors.png", "description": "截取触发错误后的页面"}], "metadata": {"purpose": "error_monitoring_demo", "expected_errors": {"console_errors": 1, "console_warnings": 1, "page_errors": 1, "network_errors": 2}}}