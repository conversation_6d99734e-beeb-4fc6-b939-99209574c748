#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的JSON读取修复验证
"""

import tkinter as tk
import json
import sys
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_json_parsing():
    """测试JSON解析逻辑"""
    print("🧪 测试JSON解析逻辑...")
    
    # 创建一个简单的tkinter窗口来模拟文本框
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 创建文本框
    text_widget = tk.Text(root)
    
    # 测试JSON
    test_json = {
        "name": "测试序列",
        "description": "测试JSON读取",
        "steps": [
            {
                "action": "goto",
                "url": "https://www.baidu.com",
                "description": "访问百度"
            },
            {
                "action": "screenshot",
                "filename": "test.png",
                "description": "截图"
            }
        ]
    }
    
    # 将JSON放入文本框
    json_str = json.dumps(test_json, ensure_ascii=False, indent=2)
    text_widget.insert(1.0, json_str)
    
    print("1. 测试从文本框读取JSON...")
    
    try:
        # 模拟execute_test中的JSON读取逻辑
        sequence_json = text_widget.get(1.0, tk.END).strip()
        print(f"   读取的JSON长度: {len(sequence_json)}")
        
        if not sequence_json:
            print("❌ 文本框为空")
            return False
        
        # 解析JSON
        current_test_sequence = json.loads(sequence_json)
        
        # 基本验证
        if not isinstance(current_test_sequence, dict):
            raise ValueError("序列必须是JSON对象")
        
        if "steps" not in current_test_sequence:
            raise ValueError("序列必须包含steps字段")
        
        if not isinstance(current_test_sequence["steps"], list):
            raise ValueError("steps必须是数组")
        
        print("✅ JSON解析成功")
        print(f"   序列名称: {current_test_sequence.get('name')}")
        print(f"   步骤数量: {len(current_test_sequence.get('steps', []))}")
        
        # 显示步骤详情
        for i, step in enumerate(current_test_sequence.get('steps', [])):
            print(f"   步骤{i+1}: {step.get('action')} - {step.get('description')}")
            if step.get('url'):
                print(f"           URL: {step.get('url')}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return False
    except ValueError as e:
        print(f"❌ 序列格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 解析异常: {e}")
        return False
    finally:
        root.destroy()

def test_modified_json():
    """测试修改后的JSON"""
    print("\n🧪 测试修改JSON内容...")
    
    root = tk.Tk()
    root.withdraw()
    
    text_widget = tk.Text(root)
    
    # 原始JSON
    original_json = {
        "name": "原始测试",
        "steps": [{"action": "goto", "url": "https://www.baidu.com", "description": "访问百度"}]
    }
    
    # 修改后的JSON
    modified_json = {
        "name": "修改后的测试",
        "steps": [
            {"action": "goto", "url": "https://www.google.com", "description": "访问谷歌"},
            {"action": "screenshot", "filename": "google.png", "description": "截图"}
        ]
    }
    
    try:
        # 先放入原始JSON
        text_widget.insert(1.0, json.dumps(original_json, ensure_ascii=False, indent=2))
        
        # 读取并解析
        sequence_json = text_widget.get(1.0, tk.END).strip()
        parsed = json.loads(sequence_json)
        print(f"✅ 原始JSON: {parsed.get('name')}, 步骤数: {len(parsed.get('steps', []))}")
        
        # 清空并放入修改后的JSON
        text_widget.delete(1.0, tk.END)
        text_widget.insert(1.0, json.dumps(modified_json, ensure_ascii=False, indent=2))
        
        # 重新读取并解析
        sequence_json = text_widget.get(1.0, tk.END).strip()
        parsed = json.loads(sequence_json)
        print(f"✅ 修改后JSON: {parsed.get('name')}, 步骤数: {len(parsed.get('steps', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修改测试失败: {e}")
        return False
    finally:
        root.destroy()

def main():
    """主测试函数"""
    print("=" * 50)
    print("🔧 JSON文本框读取修复验证")
    print("=" * 50)
    
    # 测试1: 基本JSON解析
    test1_result = test_json_parsing()
    
    # 测试2: 修改JSON内容
    test2_result = test_modified_json()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"   基本JSON解析: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   修改JSON测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 JSON读取修复验证成功！")
        print("💡 现在execute_test会正确读取文本框中的JSON内容")
        print("💡 每次执行测试都会重新解析最新的JSON内容")
    else:
        print("\n❌ 修复验证失败")

if __name__ == "__main__":
    main()
