[tool:pytest]
# Pytest配置文件

# 测试发现
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --strict-config
    --html=test-results/html-report/report.html
    --self-contained-html
    --junit-xml=test-results/junit.xml
    --cov=.
    --cov-report=html:test-results/coverage-html
    --cov-report=xml:test-results/coverage.xml
    --cov-report=term-missing

# 标记定义
markers =
    slow: 标记测试为慢速测试
    integration: 标记测试为集成测试
    ui: 标记测试为UI测试
    playwright: 标记测试为Playwright相关测试
    unit: 标记测试为单元测试
    smoke: 标记测试为冒烟测试

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:playwright.*

# 最小版本要求
minversion = 6.0

# 异步测试支持
asyncio_mode = auto

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

log_file = test-results/pytest.log
log_file_level = DEBUG
log_file_format = %(asctime)s [%(levelname)8s] %(filename)s:%(lineno)d: %(message)s
log_file_date_format = %Y-%m-%d %H:%M:%S

# 测试超时
timeout = 300

# 并行测试配置
# 使用 -n auto 启用自动并行
# pytest -n auto

# 覆盖率配置
[coverage:run]
source = .
omit = 
    tests/*
    test_*
    *_test.py
    venv/*
    .venv/*
    __pycache__/*
    .pytest_cache/*
    test-results/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod
