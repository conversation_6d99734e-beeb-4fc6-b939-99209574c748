#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目任务管理器
负责项目和编码任务的管理
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import Dict, List, Optional


class ProjectTaskManager:
    """项目任务管理器类"""
    
    def __init__(self, parent_ui, api_client):
        """
        初始化项目任务管理器
        
        Args:
            parent_ui: 父UI对象
            api_client: API客户端
        """
        self.parent_ui = parent_ui
        self.api_client = api_client
        
        # 数据
        self.projects = []
        self.coding_tasks = []
        self.selected_project = None
        self.selected_task = None
        
        # UI组件引用
        self.project_combo = None
        self.task_combo = None
        self.project_var = None
        self.task_var = None
        self.status_label = None
    
    def set_ui_components(self, project_combo, task_combo, project_var, task_var, status_label):
        """设置UI组件引用"""
        self.project_combo = project_combo
        self.task_combo = task_combo
        self.project_var = project_var
        self.task_var = task_var
        self.status_label = status_label
        
        # 绑定事件
        self.project_combo.bind('<<ComboboxSelected>>', self.on_project_changed)
        self.task_combo.bind('<<ComboboxSelected>>', self.on_task_changed)
    
    def refresh_projects(self):
        """刷新项目列表"""
        def load_projects():
            try:
                # 在主线程中更新状态
                self._safe_update_status("加载项目中...")

                # 检查API连接状态
                print(f"API认证状态: {self.api_client.is_authenticated}")
                print(f"API Token: {self.api_client.token is not None}")

                if not self.api_client.is_authenticated and not self.api_client.token:
                    self._safe_update_status("API未认证，请检查连接")
                    return

                result = self.api_client.get_projects()

                if result:
                    if 'items' in result:
                        self.projects = result['items']
                        print(f"获取到 {len(self.projects)} 个项目")
                        self._safe_call_method(self.update_project_combo)
                    else:
                        # 检查是否直接返回了项目列表
                        if isinstance(result, list):
                            self.projects = result
                            print(f"获取到 {len(self.projects)} 个项目（直接列表）")
                            self._safe_call_method(self.update_project_combo)
                        else:
                            print(f"API返回格式异常: {result}")
                            self.projects = []
                            self._safe_update_status(f"API返回格式异常: {type(result)}")
                else:
                    self.projects = []
                    self._safe_update_status("API返回空结果")

            except Exception as e:
                print(f"加载项目异常: {e}")
                self._safe_update_status(f"加载项目失败: {e}")

        threading.Thread(target=load_projects, daemon=True).start()

    def _safe_update_status(self, message):
        """线程安全的状态更新"""
        try:
            if hasattr(self, 'parent_ui') and hasattr(self.parent_ui, 'parent'):
                self.parent_ui.parent.after(0, lambda: self._update_status_label(message))
            else:
                print(f"状态更新: {message}")
        except Exception as e:
            print(f"状态更新失败: {e}")

    def _update_status_label(self, message):
        """更新状态标签"""
        try:
            if hasattr(self, 'status_label') and self.status_label:
                self.status_label.config(text=message)
        except Exception as e:
            print(f"更新状态标签失败: {e}")

    def _safe_call_method(self, method):
        """线程安全的方法调用"""
        try:
            if hasattr(self, 'parent_ui') and hasattr(self.parent_ui, 'parent'):
                self.parent_ui.parent.after(0, method)
            else:
                method()
        except Exception as e:
            print(f"方法调用失败: {e}")

    def _is_main_thread(self):
        """检查是否在主线程中"""
        import threading
        return threading.current_thread() is threading.main_thread()

    def _safe_ui_update(self, update_func):
        """线程安全的UI更新"""
        try:
            if self._is_main_thread():
                # 在主线程中直接执行
                update_func()
            else:
                # 在子线程中，使用after调度到主线程
                if hasattr(self, 'parent_ui') and hasattr(self.parent_ui, 'parent'):
                    self.parent_ui.parent.after(0, update_func)
                else:
                    print("无法调度UI更新到主线程")
        except Exception as e:
            print(f"UI更新失败: {e}")

    def update_project_combo(self):
        """更新项目下拉框"""
        try:
            if not self.projects:
                self.project_combo['values'] = []
                self.status_label.config(text="无项目数据")
                return

            # 处理项目数据，支持不同的字段名
            project_names = []
            for p in self.projects:
                # 尝试不同的字段名组合
                name = p.get('name') or p.get('Name') or p.get('projectName') or f"项目{p.get('id', '')}"
                project_id = p.get('id') or p.get('Id') or p.get('projectId', '')
                project_names.append(f"{name} (ID: {project_id})")

            print(f"项目列表: {project_names}")

            self.project_combo['values'] = project_names
            if project_names:
                self.project_combo.set(project_names[0])
                self.on_project_changed()
            self.status_label.config(text=f"已加载 {len(self.projects)} 个项目")

        except Exception as e:
            print(f"更新项目下拉框失败: {e}")
            self.status_label.config(text=f"更新项目列表失败: {e}")

    def on_project_changed(self, event=None):
        """项目选择变化"""
        selected_text = self.project_var.get()
        print(f"项目选择变化: {selected_text}")

        if selected_text:
            # 从文本中提取项目ID
            try:
                if "ID: " in selected_text:
                    project_id_str = selected_text.split("ID: ")[1].split(")")[0]
                    project_id = int(project_id_str)
                    print(f"解析的项目ID: {project_id}")

                    # 查找对应的项目，支持不同的字段名
                    self.selected_project = None
                    for p in self.projects:
                        p_id = p.get('id') or p.get('Id') or p.get('projectId')
                        print(f"比较项目ID: {p_id} == {project_id}")
                        if p_id == project_id:
                            self.selected_project = p
                            break

                    if self.selected_project:
                        print(f"找到项目: {self.selected_project}")

                        # 通知父UI项目已变更
                        if hasattr(self.parent_ui, 'on_project_selected'):
                            self.parent_ui.on_project_selected(self.selected_project)
                        
                        # 刷新编码任务列表
                        self.refresh_coding_tasks()
                    else:
                        print(f"未找到ID为 {project_id} 的项目")
                        self.status_label.config(text=f"未找到项目ID: {project_id}")
                else:
                    print("项目文本格式不正确，没有找到 'ID: ' 标识")
                    self.status_label.config(text="项目格式错误")

            except Exception as e:
                print(f"解析项目ID失败: {e}")
                self.status_label.config(text=f"解析项目失败: {e}")
        else:
            print("没有选择项目")
            self.selected_project = None
            # 通知父UI项目已清空
            if hasattr(self.parent_ui, 'on_project_cleared'):
                self.parent_ui.on_project_cleared()

    def refresh_coding_tasks(self):
        """刷新编码任务列表"""
        if not self.selected_project:
            print("警告：没有选择项目，无法加载编码任务")
            self._safe_ui_update(lambda: self.status_label.config(text="请先选择项目"))
            return

        def load_tasks():
            try:
                self._safe_update_status("加载编码任务中...")

                project_id = self.selected_project.get('id')
                result = self.api_client.get_coding_tasks(project_id)

                if result:
                    if 'data' in result and 'items' in result['data']:
                        self.coding_tasks = result['data']['items']
                        print(f"获取到 {len(self.coding_tasks)} 个编码任务")
                        self._safe_call_method(self.update_task_combo)
                    else:
                        print(f"API返回格式异常: {result}")
                        self.coding_tasks = []
                        self._safe_update_status("编码任务数据格式异常")
                else:
                    self.coding_tasks = []
                    self._safe_update_status("API返回空结果")

            except Exception as e:
                print(f"加载编码任务异常: {e}")
                self._safe_update_status(f"加载编码任务失败: {e}")

        threading.Thread(target=load_tasks, daemon=True).start()

    def update_task_combo(self):
        """更新编码任务下拉框"""
        try:
            if not self.coding_tasks:
                self.task_combo['values'] = []
                self.status_label.config(text="无编码任务数据")
                return

            task_names = []
            for task in self.coding_tasks:
                name = task.get('taskName', f"任务{task.get('id', '')}")
                task_id = task.get('id', '')
                status = task.get('status', '')
                task_names.append(f"{name} [{status}] (ID: {task_id})")

            print(f"编码任务列表: {task_names}")

            self.task_combo['values'] = task_names
            if task_names:
                self.task_combo.set(task_names[0])
                self.on_task_changed()
            self.status_label.config(text=f"已加载 {len(self.coding_tasks)} 个编码任务")

        except Exception as e:
            print(f"更新编码任务下拉框失败: {e}")
            self.status_label.config(text=f"更新编码任务列表失败: {e}")

    def on_task_changed(self, event=None):
        """编码任务选择变化"""
        selected_text = self.task_var.get()
        print(f"编码任务选择变化: {selected_text}")

        if selected_text:
            try:
                if "ID: " in selected_text:
                    task_id_str = selected_text.split("ID: ")[1].split(")")[0]
                    task_id = int(task_id_str)
                    print(f"解析的任务ID: {task_id}")

                    # 查找对应的编码任务
                    self.selected_task = None
                    for task in self.coding_tasks:
                        if task.get('id') == task_id:
                            self.selected_task = task
                            break

                    if self.selected_task:

                        # 通知父UI任务已变更
                        if hasattr(self.parent_ui, 'on_task_selected'):
                            self.parent_ui.on_task_selected(self.selected_task)
                    else:
                        print(f"未找到ID为 {task_id} 的编码任务")
                        self.status_label.config(text=f"未找到编码任务ID: {task_id}")
                else:
                    print("编码任务文本格式不正确")
                    self.status_label.config(text="编码任务格式错误")

            except Exception as e:
                print(f"解析编码任务ID失败: {e}")
                self.status_label.config(text=f"解析编码任务失败: {e}")
        else:
            print("没有选择编码任务")
            self.selected_task = None
            # 通知父UI任务已清空
            if hasattr(self.parent_ui, 'on_task_cleared'):
                self.parent_ui.on_task_cleared()

    def start_current_task(self):
        """开始当前任务"""
        if not self.selected_task:
            messagebox.showwarning("警告", "请先选择一个编码任务")
            return

        task_id = self.selected_task.get('id')
        task_name = self.selected_task.get('taskName', '未知任务')
        project_id = self.selected_project.get('id') if self.selected_project else None

        if not project_id:
            messagebox.showwarning("警告", "请先选择一个项目")
            return

        # 确认开始任务
        if not messagebox.askyesno("确认开始", f"确定要开始任务 '{task_name}' 吗？\n\n这将：\n• 将当前任务设为进行中\n• 将项目中其他任务设为暂停状态\n• 记录实际开始时间\n• 开始第一个开发步骤"):
            return

        try:
            self._safe_ui_update(lambda: self.status_label.config(text="正在开始自动化操作..."))

            # 调用新的自动化操作API
            result = self.api_client.start_automation_operation(project_id, task_id)

            if result and result.get('success'):
                self._safe_ui_update(lambda: self.status_label.config(text="✅ 自动化操作已开始"))

                # 显示详细结果
                data = result.get('data', {})
                updated_count = data.get('updatedCount', 0)
                total_tasks = data.get('totalTasks', 0)

                messagebox.showinfo("成功",
                    f"自动化操作已开始！\n\n"
                    f"当前任务: {task_name}\n"
                    f"更新任务数: {updated_count}\n"
                    f"总任务数: {total_tasks}\n\n"
                    f"当前任务已设为进行中，其他任务已暂停。\n"
                    f"第一个开发步骤已开始。")

                # 刷新任务信息
                self.refresh_current_task()
                self.refresh_coding_tasks()  # 重新加载任务列表以显示状态变化

                # 通知父UI刷新开发步骤
                if hasattr(self.parent_ui, 'refresh_development_steps'):
                    self.parent_ui.refresh_development_steps()
            else:
                error_msg = result.get('message', '未知错误') if result else '网络请求失败'
                self._safe_ui_update(lambda: self.status_label.config(text="❌ 开始自动化操作失败"))
                messagebox.showerror("失败", f"开始自动化操作失败：{error_msg}")

        except Exception as e:
            self._safe_ui_update(lambda: self.status_label.config(text="❌ 开始自动化操作异常"))
            messagebox.showerror("异常", f"开始自动化操作时发生异常：{str(e)}")

    def complete_current_task(self):
        """完成当前任务"""
        if not self.selected_task:
            messagebox.showwarning("警告", "请先选择一个编码任务")
            return

        task_id = self.selected_task.get('id')
        task_name = self.selected_task.get('taskName', '未知任务')

        # 确认完成任务
        if not messagebox.askyesno("确认完成", f"确定要完成任务 '{task_name}' 吗？\n\n这将更新任务的实际结束时间和完成状态。"):
            return

        try:
            self._safe_ui_update(lambda: self.status_label.config(text="正在完成任务..."))

            # 调用API完成任务
            result = self.api_client.complete_coding_task(task_id)

            if result and result.get('success'):
                self._safe_ui_update(lambda: self.status_label.config(text="✅ 任务已完成"))
                messagebox.showinfo("成功", f"任务 '{task_name}' 已完成！\n\n实际结束时间已记录，状态已更新为已完成。")

                # 刷新任务信息
                self.refresh_current_task()
            else:
                error_msg = result.get('message', '未知错误') if result else '网络请求失败'
                self._safe_ui_update(lambda: self.status_label.config(text="❌ 完成任务失败"))
                messagebox.showerror("失败", f"完成任务失败：{error_msg}")

        except Exception as e:
            self._safe_ui_update(lambda: self.status_label.config(text="❌ 完成任务异常"))
            messagebox.showerror("异常", f"完成任务时发生异常：{str(e)}")

    def refresh_current_task(self):
        """刷新当前任务信息"""
        if not self.selected_task:
            return

        try:
            task_id = self.selected_task.get('id')
            result = self.api_client.get_coding_task(task_id)

            if result and result.get('success'):
                self.selected_task = result['data']
                # 更新任务下拉框显示
                self.refresh_coding_tasks()
            else:
                print(f"刷新任务信息失败: {result}")

        except Exception as e:
            print(f"刷新任务信息异常: {e}")

    def get_selected_project(self):
        """获取选中的项目"""
        return self.selected_project

    def get_selected_task(self):
        """获取选中的编码任务"""
        return self.selected_task

    def load_coding_tasks(self):
        """加载编码任务（别名方法，保持兼容性）"""
        self.refresh_coding_tasks()
