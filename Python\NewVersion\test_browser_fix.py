#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试浏览器启动修复
"""

import asyncio
from playwright_test_manager import PlaywrightTestManager, SyncPlaywrightTestManager

async def test_async_browser():
    """测试异步浏览器启动"""
    print("🧪 测试异步浏览器启动...")
    
    try:
        manager = PlaywrightTestManager()
        
        # 测试启动
        result = await manager.start_browser('chromium', headless=True)
        print(f"✅ 异步浏览器启动结果: {result}")
        
        if result:
            # 测试导航
            await manager.navigate_to("https://httpbin.org/")
            print("✅ 页面导航成功")
            
            # 测试截图
            screenshot_path = await manager.take_screenshot("test_fix.png")
            print(f"✅ 截图成功: {screenshot_path}")
        
        # 关闭浏览器
        await manager.close_browser()
        print("✅ 浏览器已关闭")
        
        return result
        
    except Exception as e:
        print(f"❌ 异步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sync_browser():
    """测试同步浏览器启动"""
    print("\n🧪 测试同步浏览器启动...")
    
    try:
        manager = SyncPlaywrightTestManager()
        
        # 测试启动
        result = manager.start_browser('chromium', headless=True)
        print(f"✅ 同步浏览器启动结果: {result}")
        
        if result:
            # 测试截图
            screenshot_path = manager.take_screenshot("test_sync_fix.png")
            print(f"✅ 同步截图成功: {screenshot_path}")
        
        # 关闭浏览器
        manager.close_browser()
        print("✅ 同步浏览器已关闭")
        
        return result
        
    except Exception as e:
        print(f"❌ 同步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("=" * 50)
    print("🔧 Playwright浏览器启动修复测试")
    print("=" * 50)
    
    # 测试异步版本
    async_result = await test_async_browser()
    
    # 测试同步版本
    sync_result = test_sync_browser()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"   异步浏览器: {'✅ 成功' if async_result else '❌ 失败'}")
    print(f"   同步浏览器: {'✅ 成功' if sync_result else '❌ 失败'}")
    
    if async_result and sync_result:
        print("\n🎉 浏览器启动修复成功！")
        print("💡 现在可以正常使用Web自动化测试功能了")
    else:
        print("\n❌ 修复测试失败")
        print("💡 请检查Playwright浏览器是否正确安装:")
        print("   playwright install chromium")

if __name__ == "__main__":
    asyncio.run(main())
