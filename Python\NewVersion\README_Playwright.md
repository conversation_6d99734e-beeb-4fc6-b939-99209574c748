# Playwright自动化测试集成

本项目已集成Playwright Web自动化测试功能，提供强大的浏览器自动化和测试能力。

## 功能特性

### 🌐 多浏览器支持
- **Chromium** (Chrome/Edge)
- **Firefox**
- **WebKit** (Safari)

### 🎯 丰富的测试动作
- **页面导航**: goto, navigate
- **元素交互**: click, fill, type
- **页面截图**: screenshot
- **等待操作**: wait, wait_for_element
- **断言验证**: assert_text, assert_element

### 📊 完整的测试报告
- **HTML报告**: 详细的测试执行报告
- **JUnit XML**: CI/CD集成支持
- **JSON结果**: 程序化处理
- **覆盖率报告**: 代码覆盖率分析

### 🔧 灵活的配置
- **环境配置**: development, staging, production
- **浏览器配置**: 无头模式、视口大小、启动参数
- **测试配置**: 超时、重试、并行执行

## 安装和设置

### 1. 安装依赖
```bash
# 安装Python依赖
pip install -r requirements.txt

# 或者使用测试脚本自动安装
python run_tests.py --install-deps
```

### 2. 安装Playwright浏览器
```bash
playwright install
```

### 3. 验证安装
```bash
python run_tests.py --setup-only
```

## 使用方法

### 1. 图形界面使用

#### 启动主程序
```bash
python main.py
```

#### 打开Playwright测试UI
1. 在主界面菜单栏选择 **工具** → **Playwright Web测试**
2. 在测试UI中：
   - 选择浏览器类型（chromium/firefox/webkit）
   - 配置是否使用无头模式
   - 点击"启动浏览器"
   - 编辑或加载测试序列
   - 点击"执行测试"

### 2. 命令行使用

#### 运行所有测试
```bash
python run_tests.py
```

#### 运行特定类型的测试
```bash
# 单元测试
python run_tests.py unit

# 集成测试
python run_tests.py integration

# Playwright测试
python run_tests.py playwright

# 冒烟测试
python run_tests.py smoke
```

#### 高级选项
```bash
# 详细输出
python run_tests.py -v

# 并行执行
python run_tests.py -p

# 生成覆盖率报告
python run_tests.py -c

# 组合使用
python run_tests.py playwright -v -c
```

### 3. 编程接口使用

#### 基本使用
```python
from playwright_test_manager import SyncPlaywrightTestManager

# 创建测试管理器
manager = SyncPlaywrightTestManager("development")

# 启动浏览器
manager.start_browser("chromium", headless=False)

# 定义测试序列
test_sequence = {
    "name": "示例测试",
    "steps": [
        {
            "action": "goto",
            "url": "https://example.com",
            "description": "访问示例网站"
        },
        {
            "action": "screenshot",
            "filename": "example.png",
            "description": "截图"
        }
    ]
}

# 执行测试
result = manager.execute_test_sequence(test_sequence)
print(f"测试结果: {result['success']}")

# 关闭浏览器
manager.close_browser()
```

#### 集成到现有自动化管理器
```python
from enhanced_automation_manager import EnhancedAutomationManager

# 创建增强自动化管理器（已集成Playwright）
manager = EnhancedAutomationManager(api_client)

# 启动Playwright浏览器
manager.start_playwright_browser("chromium", headless=False)

# 执行Web测试序列
result = manager.execute_web_test_sequence(test_sequence)

# 获取测试结果
results = manager.get_playwright_test_results()

# 保存测试结果
manager.save_playwright_test_results("test_results.json")
```

## 测试序列格式

### 标准格式
```json
{
  "name": "测试名称",
  "description": "测试描述",
  "steps": [
    {
      "action": "goto",
      "url": "https://example.com",
      "description": "访问网站"
    },
    {
      "action": "click",
      "selector": "#button",
      "description": "点击按钮"
    },
    {
      "action": "fill",
      "selector": "input[name='username']",
      "text": "testuser",
      "description": "填写用户名"
    },
    {
      "action": "screenshot",
      "filename": "result.png",
      "description": "截图"
    }
  ]
}
```

### 增强格式（ActionType + LogicType）
```json
{
  "name": "增强格式测试",
  "steps": [
    {
      "StepOrder": 1,
      "ActionType": "web_goto",
      "LogicType": null,
      "Description": "访问网站",
      "Parameters": {
        "url": "https://example.com"
      },
      "TimeoutSeconds": 10,
      "MaxRetries": 3,
      "IsActive": true
    }
  ]
}
```

## 支持的测试动作

| 动作类型 | 参数 | 描述 |
|---------|------|------|
| `goto` | `url` | 导航到指定URL |
| `click` | `selector` | 点击元素 |
| `fill` | `selector`, `text` | 填写输入框 |
| `type` | `selector`, `text` | 输入文本 |
| `screenshot` | `filename` | 截图 |
| `wait` | `timeout` | 等待指定时间 |
| `assert_text` | `selector`, `text` | 断言文本内容 |

## 配置文件

### playwright.config.py
主要配置文件，包含：
- 浏览器配置
- 超时设置
- 输出目录
- 报告配置

### pytest.ini
Pytest配置文件，包含：
- 测试发现规则
- 输出格式
- 标记定义
- 覆盖率配置

## 目录结构

```
Python/NewVersion/
├── playwright_config.py          # Playwright配置
├── playwright_test_manager.py    # 测试管理器
├── playwright_test_ui.py         # 图形界面
├── run_tests.py                  # 测试运行脚本
├── pytest.ini                   # Pytest配置
├── tests/                        # 测试文件
│   ├── __init__.py
│   ├── conftest.py              # 测试配置
│   └── test_playwright_manager.py
├── test_sequences/              # 测试序列
│   ├── example_web_test.json
│   └── enhanced_format_test.json
└── test-results/               # 测试结果
    ├── screenshots/
    ├── videos/
    ├── html-report/
    └── coverage-html/
```

## 故障排除

### 常见问题

1. **Playwright未安装**
   ```bash
   pip install playwright
   playwright install
   ```

2. **浏览器启动失败**
   - 检查系统权限
   - 尝试无头模式
   - 查看错误日志

3. **元素定位失败**
   - 检查选择器语法
   - 增加等待时间
   - 使用更具体的选择器

4. **测试超时**
   - 增加超时时间
   - 检查网络连接
   - 优化测试步骤

### 调试技巧

1. **启用详细日志**
   ```bash
   python run_tests.py -v
   ```

2. **使用非无头模式**
   - 在UI中取消勾选"无头模式"
   - 或在代码中设置 `headless=False`

3. **查看截图和视频**
   - 测试失败时会自动保存截图
   - 检查 `test-results/screenshots/` 目录

## 扩展开发

### 添加新的测试动作
1. 在 `playwright_test_manager.py` 中的 `_execute_test_step` 方法添加新动作
2. 更新文档和示例
3. 添加相应的测试用例

### 自定义报告格式
1. 修改 `playwright_test_ui.py` 中的 `generate_html_report` 方法
2. 或创建新的报告生成器

### 集成CI/CD
使用生成的JUnit XML报告：
```yaml
# GitHub Actions示例
- name: Run Playwright Tests
  run: python run_tests.py playwright --coverage
- name: Upload Test Results
  uses: actions/upload-artifact@v2
  with:
    name: test-results
    path: test-results/
```

## 许可证

本项目遵循MIT许可证。
