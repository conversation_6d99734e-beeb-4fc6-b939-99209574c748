#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Playwright测试UI界面
提供图形化界面来管理和执行Web自动化测试
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import json
import threading
from pathlib import Path
from datetime import datetime
import traceback

try:
    from playwright_test_manager import SyncPlaywrightTestManager, PLAYWRIGHT_AVAILABLE
except ImportError:
    PLAYWRIGHT_AVAILABLE = False


class PlaywrightTestUI:
    """Playwright测试UI界面"""
    
    def __init__(self, parent=None, automation_manager=None):
        """初始化UI界面"""
        self.parent = parent
        self.automation_manager = automation_manager

        # 创建容器 - 如果有父容器则嵌入，否则创建独立窗口
        if parent and hasattr(parent, 'pack'):
            # 嵌入模式：使用父容器作为主容器
            self.window = parent
            self.is_embedded = True
        else:
            # 独立窗口模式
            if parent:
                self.window = tk.Toplevel(parent)
            else:
                self.window = tk.Tk()
            self.window.title("Playwright Web自动化测试")
            self.window.geometry("1000x700")
            self.is_embedded = False
        
        # 测试管理器
        self.test_manager = None
        self.is_browser_running = False
        
        # 测试数据
        self.current_test_sequence = None
        self.test_results = []

        # 错误监控
        self.error_monitoring_enabled = True
        
        # 创建界面
        self.create_widgets()
        self.setup_layout()
        
        # 检查Playwright可用性
        self.check_playwright_availability()
    
    def check_playwright_availability(self):
        """检查Playwright可用性"""
        if not PLAYWRIGHT_AVAILABLE:
            messagebox.showwarning(
                "Playwright不可用",
                "Playwright未安装或导入失败。\n请运行以下命令安装：\npip install playwright\nplaywright install"
            )
            self.status_var.set("状态: Playwright不可用")
        else:
            self.status_var.set("状态: 就绪")
    
    def create_widgets(self):
        """创建界面组件"""
        # 状态栏
        self.status_var = tk.StringVar(value="状态: 初始化中...")
        
        # 浏览器控制区域
        browser_frame = ttk.LabelFrame(self.window, text="浏览器控制", padding=10)
        
        # 浏览器选择
        ttk.Label(browser_frame, text="浏览器:").grid(row=0, column=0, sticky="w", padx=(0, 5))
        self.browser_var = tk.StringVar(value="chromium")
        browser_combo = ttk.Combobox(browser_frame, textvariable=self.browser_var, 
                                   values=["chromium", "firefox", "webkit"], state="readonly", width=15)
        browser_combo.grid(row=0, column=1, padx=(0, 10))
        
        # 无头模式
        self.headless_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(browser_frame, text="无头模式", variable=self.headless_var).grid(row=0, column=2, padx=(0, 10))

        # 错误监控
        self.error_monitor_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(browser_frame, text="错误监控", variable=self.error_monitor_var).grid(row=0, column=3, padx=(0, 10))
        
        # 浏览器控制按钮
        ttk.Button(browser_frame, text="启动浏览器", command=self.start_browser).grid(row=0, column=4, padx=5)
        ttk.Button(browser_frame, text="关闭浏览器", command=self.close_browser).grid(row=0, column=5, padx=5)
        
        self.browser_frame = browser_frame
        
        # 测试序列编辑区域
        sequence_frame = ttk.LabelFrame(self.window, text="测试序列编辑", padding=10)
        
        # 序列名称
        ttk.Label(sequence_frame, text="序列名称:").grid(row=0, column=0, sticky="w", padx=(0, 5))
        self.sequence_name_var = tk.StringVar(value="新测试序列")
        ttk.Entry(sequence_frame, textvariable=self.sequence_name_var, width=30).grid(row=0, column=1, sticky="ew", padx=(0, 10))
        
        # 序列描述
        ttk.Label(sequence_frame, text="描述:").grid(row=0, column=2, sticky="w", padx=(10, 5))
        self.sequence_desc_var = tk.StringVar(value="测试序列描述")
        ttk.Entry(sequence_frame, textvariable=self.sequence_desc_var, width=40).grid(row=0, column=3, sticky="ew")
        
        # 序列JSON编辑器
        ttk.Label(sequence_frame, text="测试序列JSON:").grid(row=1, column=0, sticky="nw", pady=(10, 0))
        # 根据是否嵌入调整高度
        text_height = 10 if self.is_embedded else 15
        self.sequence_text = scrolledtext.ScrolledText(sequence_frame, height=text_height, width=80)
        self.sequence_text.grid(row=2, column=0, columnspan=4, sticky="nsew", pady=(5, 0))
        
        # 设置默认测试序列
        self.load_default_sequence()
        
        # 序列操作按钮
        button_frame = ttk.Frame(sequence_frame)
        button_frame.grid(row=3, column=0, columnspan=4, pady=(10, 0), sticky="ew")
        
        ttk.Button(button_frame, text="加载序列", command=self.load_sequence).pack(side="left", padx=(0, 5))
        ttk.Button(button_frame, text="保存序列", command=self.save_sequence).pack(side="left", padx=5)
        ttk.Button(button_frame, text="验证序列", command=self.validate_sequence).pack(side="left", padx=5)
        ttk.Button(button_frame, text="执行测试", command=self.execute_test).pack(side="left", padx=5)
        
        sequence_frame.columnconfigure(1, weight=1)
        sequence_frame.columnconfigure(3, weight=2)
        sequence_frame.rowconfigure(2, weight=1)
        
        self.sequence_frame = sequence_frame
        
        # 测试结果区域
        results_frame = ttk.LabelFrame(self.window, text="测试结果与错误监控", padding=10)

        # 创建结果选项卡
        self.results_notebook = ttk.Notebook(results_frame)
        self.results_notebook.grid(row=0, column=0, columnspan=3, sticky="nsew")

        # 测试结果选项卡
        results_tab = ttk.Frame(self.results_notebook)
        self.results_notebook.add(results_tab, text="📊 测试结果")

        # 根据是否嵌入调整高度
        results_height = 6 if self.is_embedded else 10
        self.results_text = scrolledtext.ScrolledText(results_tab, height=results_height, width=80)
        self.results_text.pack(fill=tk.BOTH, expand=True)

        # 错误监控选项卡
        errors_tab = ttk.Frame(self.results_notebook)
        self.results_notebook.add(errors_tab, text="🔍 错误监控")

        # 错误类型选择
        error_control_frame = ttk.Frame(errors_tab)
        error_control_frame.pack(fill="x", pady=(0, 5))

        ttk.Label(error_control_frame, text="错误类型:").pack(side="left")
        self.error_type_var = tk.StringVar(value="all")
        error_type_combo = ttk.Combobox(error_control_frame, textvariable=self.error_type_var,
                                       values=["all", "console", "page", "network"], state="readonly", width=15)
        error_type_combo.pack(side="left", padx=(5, 10))
        error_type_combo.bind('<<ComboboxSelected>>', self.refresh_error_display)

        ttk.Button(error_control_frame, text="刷新错误", command=self.refresh_error_display).pack(side="left", padx=5)
        ttk.Button(error_control_frame, text="清空错误", command=self.clear_errors).pack(side="left", padx=5)
        ttk.Button(error_control_frame, text="保存错误报告", command=self.save_error_report).pack(side="left", padx=5)

        # 错误显示区域
        self.errors_text = scrolledtext.ScrolledText(errors_tab, height=results_height, width=80)
        self.errors_text.pack(fill=tk.BOTH, expand=True)
        
        # 结果操作按钮
        result_button_frame = ttk.Frame(results_frame)
        result_button_frame.grid(row=1, column=0, columnspan=3, pady=(10, 0), sticky="ew")
        
        ttk.Button(result_button_frame, text="清空结果", command=self.clear_results).pack(side="left", padx=(0, 5))
        ttk.Button(result_button_frame, text="保存结果", command=self.save_results).pack(side="left", padx=5)
        ttk.Button(result_button_frame, text="导出报告", command=self.export_report).pack(side="left", padx=5)
        
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        self.results_frame = results_frame
        
        # 状态栏
        status_frame = ttk.Frame(self.window)
        ttk.Label(status_frame, textvariable=self.status_var).pack(side="left")
        self.status_frame = status_frame
    
    def setup_layout(self):
        """设置布局"""
        if self.is_embedded:
            # 嵌入模式：调整边距和间距
            self.browser_frame.pack(fill="x", padx=5, pady=(5, 3))
            self.sequence_frame.pack(fill="both", expand=True, padx=5, pady=3)
            self.results_frame.pack(fill="both", expand=True, padx=5, pady=3)
            self.status_frame.pack(fill="x", padx=5, pady=(3, 5))
        else:
            # 独立窗口模式：使用原有布局
            self.browser_frame.pack(fill="x", padx=10, pady=(10, 5))
            self.sequence_frame.pack(fill="both", expand=True, padx=10, pady=5)
            self.results_frame.pack(fill="both", expand=True, padx=10, pady=5)
            self.status_frame.pack(fill="x", padx=10, pady=(5, 10))
    
    def load_default_sequence(self):
        """加载默认测试序列"""
        default_sequence = {
            "name": "示例Web测试",
            "description": "演示基本Web自动化操作",
            "steps": [
                {
                    "action": "goto",
                    "url": "https://httpbin.org/",
                    "description": "访问httpbin测试网站"
                },
                {
                    "action": "screenshot",
                    "filename": "homepage.png",
                    "description": "截取首页截图"
                },
                {
                    "action": "wait",
                    "timeout": 2000,
                    "description": "等待2秒"
                },
                {
                    "action": "goto",
                    "url": "https://httpbin.org/forms/post",
                    "description": "访问表单页面"
                },
                {
                    "action": "fill",
                    "selector": "input[name='custname']",
                    "text": "测试用户",
                    "description": "填写客户姓名"
                },
                {
                    "action": "screenshot",
                    "filename": "form_filled.png",
                    "description": "截取填写后的表单"
                }
            ]
        }
        
        self.sequence_text.delete(1.0, tk.END)
        self.sequence_text.insert(1.0, json.dumps(default_sequence, ensure_ascii=False, indent=2))
    
    def start_browser(self):
        """启动浏览器"""
        if not PLAYWRIGHT_AVAILABLE:
            messagebox.showerror("错误", "Playwright不可用")
            return
        
        def start_browser_thread():
            try:
                self.status_var.set("状态: 启动浏览器中...")
                
                if not self.test_manager:
                    self.test_manager = SyncPlaywrightTestManager("development")
                
                browser_name = self.browser_var.get()
                headless = self.headless_var.get()

                success = self.test_manager.start_browser(browser_name, headless)

                # 设置错误监控回调
                if success and self.error_monitor_var.get():
                    self.setup_error_monitoring()
                
                if success:
                    self.is_browser_running = True
                    self.status_var.set(f"状态: {browser_name}浏览器运行中")
                    self.log_result(f"✅ {browser_name}浏览器启动成功")
                else:
                    self.status_var.set("状态: 浏览器启动失败")
                    self.log_result("❌ 浏览器启动失败")
                    
            except Exception as e:
                self.status_var.set("状态: 浏览器启动异常")
                self.log_result(f"❌ 浏览器启动异常: {e}")
        
        threading.Thread(target=start_browser_thread, daemon=True).start()
    
    def close_browser(self):
        """关闭浏览器"""
        if self.test_manager and self.is_browser_running:
            try:
                self.test_manager.close_browser()
                self.is_browser_running = False
                self.status_var.set("状态: 浏览器已关闭")
                self.log_result("✅ 浏览器已关闭")
            except Exception as e:
                self.log_result(f"⚠️ 关闭浏览器异常: {e}")
    
    def validate_sequence(self):
        """验证测试序列"""
        try:
            sequence_json = self.sequence_text.get(1.0, tk.END).strip()
            sequence = json.loads(sequence_json)
            
            # 基本验证
            if not isinstance(sequence, dict):
                raise ValueError("序列必须是JSON对象")
            
            if "steps" not in sequence:
                raise ValueError("序列必须包含steps字段")
            
            if not isinstance(sequence["steps"], list):
                raise ValueError("steps必须是数组")
            
            # 验证每个步骤
            for i, step in enumerate(sequence["steps"]):
                if not isinstance(step, dict):
                    raise ValueError(f"步骤{i+1}必须是对象")
                
                if "action" not in step:
                    raise ValueError(f"步骤{i+1}缺少action字段")
            
            self.current_test_sequence = sequence
            self.log_result("✅ 测试序列验证通过")
            messagebox.showinfo("验证成功", "测试序列格式正确")
            
        except json.JSONDecodeError as e:
            error_msg = f"JSON格式错误: {e}"
            self.log_result(f"❌ {error_msg}")
            messagebox.showerror("验证失败", error_msg)
        except ValueError as e:
            error_msg = f"序列格式错误: {e}"
            self.log_result(f"❌ {error_msg}")
            messagebox.showerror("验证失败", error_msg)
        except Exception as e:
            error_msg = f"验证异常: {e}"
            self.log_result(f"❌ {error_msg}")
            messagebox.showerror("验证失败", error_msg)

    def execute_test(self):
        """执行测试"""
        if not self.is_browser_running:
            messagebox.showwarning("警告", "请先启动浏览器")
            return

        if not self.current_test_sequence:
            if not self.validate_sequence():
                return

        def execute_test_thread():
            try:
                self.status_var.set("状态: 执行测试中...")
                self.log_result(f"\n🚀 开始执行测试: {self.current_test_sequence.get('name', '未命名测试')}")

                result = self.test_manager.execute_test_sequence(self.current_test_sequence)

                if result.get("success", False):
                    self.status_var.set("状态: 测试执行完成")
                    self.log_result("✅ 测试执行成功")
                else:
                    self.status_var.set("状态: 测试执行失败")
                    self.log_result(f"❌ 测试执行失败: {result.get('error', '未知错误')}")

                # 显示详细结果
                self.display_test_result(result)
                self.test_results.append(result)

            except Exception as e:
                self.status_var.set("状态: 测试执行异常")
                self.log_result(f"❌ 测试执行异常: {e}")
                traceback.print_exc()

        threading.Thread(target=execute_test_thread, daemon=True).start()

    def display_test_result(self, result):
        """显示测试结果详情"""
        self.log_result(f"\n📊 测试结果详情:")
        self.log_result(f"   测试名称: {result.get('test_name', '未知')}")
        self.log_result(f"   开始时间: {result.get('start_time', '未知')}")
        self.log_result(f"   结束时间: {result.get('end_time', '未知')}")
        self.log_result(f"   总体结果: {'成功' if result.get('success') else '失败'}")

        steps = result.get('steps', [])
        self.log_result(f"   执行步骤: {len(steps)}个")

        for i, step in enumerate(steps):
            status = "✅" if step.get('success') else "❌"
            self.log_result(f"     {i+1}. {status} {step.get('action', '未知动作')}: {step.get('description', '无描述')}")
            if not step.get('success') and step.get('error'):
                self.log_result(f"        错误: {step['error']}")

    def load_sequence(self):
        """加载测试序列文件"""
        file_path = filedialog.askopenfilename(
            title="选择测试序列文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    sequence = json.load(f)

                self.sequence_text.delete(1.0, tk.END)
                self.sequence_text.insert(1.0, json.dumps(sequence, ensure_ascii=False, indent=2))

                if "name" in sequence:
                    self.sequence_name_var.set(sequence["name"])
                if "description" in sequence:
                    self.sequence_desc_var.set(sequence["description"])

                self.log_result(f"✅ 已加载测试序列: {file_path}")

            except Exception as e:
                error_msg = f"加载文件失败: {e}"
                self.log_result(f"❌ {error_msg}")
                messagebox.showerror("加载失败", error_msg)

    def save_sequence(self):
        """保存测试序列文件"""
        try:
            sequence_json = self.sequence_text.get(1.0, tk.END).strip()
            sequence = json.loads(sequence_json)

            # 更新名称和描述
            sequence["name"] = self.sequence_name_var.get()
            sequence["description"] = self.sequence_desc_var.get()

            file_path = filedialog.asksaveasfilename(
                title="保存测试序列",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(sequence, f, ensure_ascii=False, indent=2)

                self.log_result(f"✅ 测试序列已保存: {file_path}")

        except json.JSONDecodeError as e:
            error_msg = f"JSON格式错误: {e}"
            self.log_result(f"❌ {error_msg}")
            messagebox.showerror("保存失败", error_msg)
        except Exception as e:
            error_msg = f"保存失败: {e}"
            self.log_result(f"❌ {error_msg}")
            messagebox.showerror("保存失败", error_msg)

    def clear_results(self):
        """清空测试结果"""
        self.results_text.delete(1.0, tk.END)
        self.test_results.clear()
        self.log_result("🧹 测试结果已清空")

    def save_results(self):
        """保存测试结果"""
        if not self.test_results:
            messagebox.showinfo("提示", "没有测试结果可保存")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存测试结果",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.test_results, f, ensure_ascii=False, indent=2)

                self.log_result(f"✅ 测试结果已保存: {file_path}")

            except Exception as e:
                error_msg = f"保存结果失败: {e}"
                self.log_result(f"❌ {error_msg}")
                messagebox.showerror("保存失败", error_msg)

    def export_report(self):
        """导出HTML测试报告"""
        if not self.test_results:
            messagebox.showinfo("提示", "没有测试结果可导出")
            return

        file_path = filedialog.asksaveasfilename(
            title="导出测试报告",
            defaultextension=".html",
            filetypes=[("HTML文件", "*.html"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                self.generate_html_report(file_path)
                self.log_result(f"✅ 测试报告已导出: {file_path}")

            except Exception as e:
                error_msg = f"导出报告失败: {e}"
                self.log_result(f"❌ {error_msg}")
                messagebox.showerror("导出失败", error_msg)

    def generate_html_report(self, file_path):
        """生成HTML测试报告"""
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Playwright自动化测试报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .test-result {{ border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }}
        .success {{ border-left: 5px solid #4CAF50; }}
        .failure {{ border-left: 5px solid #f44336; }}
        .step {{ margin: 5px 0; padding: 5px; background-color: #f9f9f9; }}
        .step.success {{ background-color: #e8f5e8; }}
        .step.failure {{ background-color: #ffeaea; }}
        .timestamp {{ color: #666; font-size: 0.9em; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Playwright自动化测试报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>

    <div class="summary">
        <h2>测试摘要</h2>
        <p>总测试数: {len(self.test_results)}</p>
        <p>成功测试: {sum(1 for r in self.test_results if r.get('success'))}</p>
        <p>失败测试: {sum(1 for r in self.test_results if not r.get('success'))}</p>
    </div>

    <div class="results">
        <h2>详细结果</h2>
"""

        for i, result in enumerate(self.test_results):
            success_class = "success" if result.get('success') else "failure"
            status_text = "成功" if result.get('success') else "失败"

            html_content += f"""
        <div class="test-result {success_class}">
            <h3>测试 {i+1}: {result.get('test_name', '未命名测试')} - {status_text}</h3>
            <p class="timestamp">开始时间: {result.get('start_time', '未知')}</p>
            <p class="timestamp">结束时间: {result.get('end_time', '未知')}</p>

            <h4>执行步骤:</h4>
"""

            for step in result.get('steps', []):
                step_class = "success" if step.get('success') else "failure"
                step_status = "✅" if step.get('success') else "❌"

                html_content += f"""
            <div class="step {step_class}">
                {step_status} {step.get('action', '未知动作')}: {step.get('description', '无描述')}
"""
                if not step.get('success') and step.get('error'):
                    html_content += f"<br><strong>错误:</strong> {step['error']}"

                html_content += "</div>"

            html_content += "</div>"

        html_content += """
    </div>
</body>
</html>
"""

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

    def setup_error_monitoring(self):
        """设置错误监控"""
        if not self.test_manager:
            return

        try:
            # 添加错误回调
            self.test_manager.add_callback('console_message', self.on_console_message)
            self.test_manager.add_callback('page_error', self.on_page_error)
            self.test_manager.add_callback('network_error', self.on_network_error)

            self.log_result("✅ 错误监控已启用")

        except Exception as e:
            self.log_result(f"⚠️ 错误监控设置失败: {e}")

    def on_console_message(self, console_data):
        """处理控制台消息"""
        msg_type = console_data.get('type', 'log')
        text = console_data.get('text', '')
        timestamp = console_data.get('timestamp', '')

        if msg_type in ['error', 'warning']:
            self.log_error(f"[控制台{msg_type.upper()}] {text}", timestamp)

    def on_page_error(self, error_data):
        """处理页面错误"""
        message = error_data.get('message', '')
        timestamp = error_data.get('timestamp', '')

        self.log_error(f"[页面错误] {message}", timestamp)

    def on_network_error(self, error_data):
        """处理网络错误"""
        if 'failure' in error_data:
            # 请求失败
            url = error_data.get('url', '')
            failure = error_data.get('failure', '')
            timestamp = error_data.get('timestamp', '')
            self.log_error(f"[网络错误] {url} - {failure}", timestamp)
        elif 'status' in error_data and error_data['status'] >= 400:
            # HTTP错误状态码
            url = error_data.get('url', '')
            status = error_data.get('status', '')
            timestamp = error_data.get('timestamp', '')
            self.log_error(f"[HTTP错误] {status} {url}", timestamp)

    def log_error(self, message, timestamp=""):
        """记录错误到错误监控区域"""
        if timestamp:
            timestamp_str = timestamp.split('T')[1].split('.')[0] if 'T' in timestamp else timestamp
            error_msg = f"[{timestamp_str}] {message}"
        else:
            error_msg = f"[{datetime.now().strftime('%H:%M:%S')}] {message}"

        # 同时记录到结果区域和错误区域
        self.log_result(f"🔍 {message}")

        # 更新错误显示
        self.window.after(0, lambda: self.refresh_error_display())

    def refresh_error_display(self, event=None):
        """刷新错误显示"""
        if not self.test_manager:
            return

        try:
            error_type = self.error_type_var.get()

            # 清空错误显示区域
            self.errors_text.delete(1.0, tk.END)

            if error_type == "all":
                all_errors = self.test_manager.get_all_errors()

                # 显示错误统计
                self.errors_text.insert(tk.END, f"📊 错误统计:\n")
                self.errors_text.insert(tk.END, f"   控制台错误: {len(all_errors.get('console_errors', []))}\n")
                self.errors_text.insert(tk.END, f"   控制台警告: {len(all_errors.get('console_warnings', []))}\n")
                self.errors_text.insert(tk.END, f"   页面错误: {len(all_errors.get('page_errors', []))}\n")
                self.errors_text.insert(tk.END, f"   网络错误: {len(all_errors.get('network_errors', []))}\n")
                self.errors_text.insert(tk.END, f"   总计: {all_errors.get('total_errors', 0)}\n\n")

                # 显示最近的错误
                self.errors_text.insert(tk.END, "🔍 最近的错误:\n")

                # 合并所有错误并按时间排序
                all_error_list = []
                for console_error in all_errors.get('console_errors', []):
                    all_error_list.append(('控制台错误', console_error))
                for console_warning in all_errors.get('console_warnings', []):
                    all_error_list.append(('控制台警告', console_warning))
                for page_error in all_errors.get('page_errors', []):
                    all_error_list.append(('页面错误', page_error))
                for network_error in all_errors.get('network_errors', []):
                    all_error_list.append(('网络错误', network_error))

                # 按时间戳排序（最新的在前）
                all_error_list.sort(key=lambda x: x[1].get('timestamp', ''), reverse=True)

                # 显示最近20个错误
                for error_type_name, error_data in all_error_list[:20]:
                    timestamp = error_data.get('timestamp', '').split('T')[1].split('.')[0] if 'T' in error_data.get('timestamp', '') else ''
                    if error_type_name == '控制台错误' or error_type_name == '控制台警告':
                        text = error_data.get('text', '')
                        self.errors_text.insert(tk.END, f"[{timestamp}] {error_type_name}: {text}\n")
                    elif error_type_name == '页面错误':
                        message = error_data.get('message', '')
                        self.errors_text.insert(tk.END, f"[{timestamp}] {error_type_name}: {message}\n")
                    elif error_type_name == '网络错误':
                        if 'failure' in error_data:
                            url = error_data.get('url', '')
                            failure = error_data.get('failure', '')
                            self.errors_text.insert(tk.END, f"[{timestamp}] {error_type_name}: {url} - {failure}\n")
                        else:
                            url = error_data.get('url', '')
                            status = error_data.get('status', '')
                            self.errors_text.insert(tk.END, f"[{timestamp}] {error_type_name}: {status} {url}\n")

            elif error_type == "console":
                console_logs = self.test_manager.get_console_logs()
                self.errors_text.insert(tk.END, f"📝 控制台日志 ({len(console_logs)} 条):\n\n")
                for log in console_logs[-50:]:  # 显示最近50条
                    timestamp = log.get('timestamp', '').split('T')[1].split('.')[0] if 'T' in log.get('timestamp', '') else ''
                    log_type = log.get('type', 'log').upper()
                    text = log.get('text', '')
                    self.errors_text.insert(tk.END, f"[{timestamp}] {log_type}: {text}\n")

            elif error_type == "page":
                page_errors = self.test_manager.get_page_errors()
                self.errors_text.insert(tk.END, f"❌ 页面错误 ({len(page_errors)} 条):\n\n")
                for error in page_errors:
                    timestamp = error.get('timestamp', '').split('T')[1].split('.')[0] if 'T' in error.get('timestamp', '') else ''
                    message = error.get('message', '')
                    self.errors_text.insert(tk.END, f"[{timestamp}] {message}\n")
                    if error.get('stack'):
                        self.errors_text.insert(tk.END, f"   堆栈: {error['stack']}\n")
                    self.errors_text.insert(tk.END, "\n")

            elif error_type == "network":
                network_errors = self.test_manager.get_network_errors()
                self.errors_text.insert(tk.END, f"🌐 网络错误 ({len(network_errors)} 条):\n\n")
                for error in network_errors:
                    timestamp = error.get('timestamp', '').split('T')[1].split('.')[0] if 'T' in error.get('timestamp', '') else ''
                    if 'failure' in error:
                        url = error.get('url', '')
                        method = error.get('method', 'GET')
                        failure = error.get('failure', '')
                        self.errors_text.insert(tk.END, f"[{timestamp}] {method} {url}\n")
                        self.errors_text.insert(tk.END, f"   失败原因: {failure}\n\n")
                    else:
                        url = error.get('url', '')
                        status = error.get('status', '')
                        status_text = error.get('status_text', '')
                        self.errors_text.insert(tk.END, f"[{timestamp}] {status} {status_text}\n")
                        self.errors_text.insert(tk.END, f"   URL: {url}\n\n")

            # 滚动到底部
            self.errors_text.see(tk.END)

        except Exception as e:
            self.errors_text.delete(1.0, tk.END)
            self.errors_text.insert(tk.END, f"❌ 刷新错误显示失败: {e}")

    def clear_errors(self):
        """清空错误记录"""
        if self.test_manager:
            self.test_manager.clear_errors()
            self.refresh_error_display()
            self.log_result("🧹 错误记录已清空")

    def save_error_report(self):
        """保存错误报告"""
        if not self.test_manager:
            messagebox.showinfo("提示", "请先启动浏览器")
            return

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"error_report_{timestamp}.json"

            report_path = self.test_manager.save_error_report(filename)
            self.log_result(f"✅ 错误报告已保存: {report_path}")
            messagebox.showinfo("成功", f"错误报告已保存到:\n{report_path}")

        except Exception as e:
            error_msg = f"保存错误报告失败: {e}"
            self.log_result(f"❌ {error_msg}")
            messagebox.showerror("保存失败", error_msg)

    def log_result(self, message):
        """记录结果到界面"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.results_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.results_text.see(tk.END)
        self.window.update_idletasks()

    def run(self):
        """运行界面"""
        if not self.is_embedded and not self.parent:
            self.window.mainloop()


def main():
    """主函数"""
    app = PlaywrightTestUI()
    app.run()


if __name__ == "__main__":
    main()
