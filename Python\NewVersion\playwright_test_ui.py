#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Playwright测试UI界面
提供图形化界面来管理和执行Web自动化测试
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import json
import threading
from pathlib import Path
from datetime import datetime
import traceback

try:
    from playwright_test_manager import SyncPlaywrightTestManager, PLAYWRIGHT_AVAILABLE
except ImportError:
    PLAYWRIGHT_AVAILABLE = False


class PlaywrightTestUI:
    """Playwright测试UI界面"""
    
    def __init__(self, parent=None, automation_manager=None):
        """初始化UI界面"""
        self.parent = parent
        self.automation_manager = automation_manager
        
        # 创建窗口
        if parent:
            self.window = tk.Toplevel(parent)
        else:
            self.window = tk.Tk()
        
        self.window.title("Playwright Web自动化测试")
        self.window.geometry("1000x700")
        
        # 测试管理器
        self.test_manager = None
        self.is_browser_running = False
        
        # 测试数据
        self.current_test_sequence = None
        self.test_results = []
        
        # 创建界面
        self.create_widgets()
        self.setup_layout()
        
        # 检查Playwright可用性
        self.check_playwright_availability()
    
    def check_playwright_availability(self):
        """检查Playwright可用性"""
        if not PLAYWRIGHT_AVAILABLE:
            messagebox.showwarning(
                "Playwright不可用",
                "Playwright未安装或导入失败。\n请运行以下命令安装：\npip install playwright\nplaywright install"
            )
            self.status_var.set("状态: Playwright不可用")
        else:
            self.status_var.set("状态: 就绪")
    
    def create_widgets(self):
        """创建界面组件"""
        # 状态栏
        self.status_var = tk.StringVar(value="状态: 初始化中...")
        
        # 浏览器控制区域
        browser_frame = ttk.LabelFrame(self.window, text="浏览器控制", padding=10)
        
        # 浏览器选择
        ttk.Label(browser_frame, text="浏览器:").grid(row=0, column=0, sticky="w", padx=(0, 5))
        self.browser_var = tk.StringVar(value="chromium")
        browser_combo = ttk.Combobox(browser_frame, textvariable=self.browser_var, 
                                   values=["chromium", "firefox", "webkit"], state="readonly", width=15)
        browser_combo.grid(row=0, column=1, padx=(0, 10))
        
        # 无头模式
        self.headless_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(browser_frame, text="无头模式", variable=self.headless_var).grid(row=0, column=2, padx=(0, 10))
        
        # 浏览器控制按钮
        ttk.Button(browser_frame, text="启动浏览器", command=self.start_browser).grid(row=0, column=3, padx=5)
        ttk.Button(browser_frame, text="关闭浏览器", command=self.close_browser).grid(row=0, column=4, padx=5)
        
        self.browser_frame = browser_frame
        
        # 测试序列编辑区域
        sequence_frame = ttk.LabelFrame(self.window, text="测试序列编辑", padding=10)
        
        # 序列名称
        ttk.Label(sequence_frame, text="序列名称:").grid(row=0, column=0, sticky="w", padx=(0, 5))
        self.sequence_name_var = tk.StringVar(value="新测试序列")
        ttk.Entry(sequence_frame, textvariable=self.sequence_name_var, width=30).grid(row=0, column=1, sticky="ew", padx=(0, 10))
        
        # 序列描述
        ttk.Label(sequence_frame, text="描述:").grid(row=0, column=2, sticky="w", padx=(10, 5))
        self.sequence_desc_var = tk.StringVar(value="测试序列描述")
        ttk.Entry(sequence_frame, textvariable=self.sequence_desc_var, width=40).grid(row=0, column=3, sticky="ew")
        
        # 序列JSON编辑器
        ttk.Label(sequence_frame, text="测试序列JSON:").grid(row=1, column=0, sticky="nw", pady=(10, 0))
        self.sequence_text = scrolledtext.ScrolledText(sequence_frame, height=15, width=80)
        self.sequence_text.grid(row=2, column=0, columnspan=4, sticky="nsew", pady=(5, 0))
        
        # 设置默认测试序列
        self.load_default_sequence()
        
        # 序列操作按钮
        button_frame = ttk.Frame(sequence_frame)
        button_frame.grid(row=3, column=0, columnspan=4, pady=(10, 0), sticky="ew")
        
        ttk.Button(button_frame, text="加载序列", command=self.load_sequence).pack(side="left", padx=(0, 5))
        ttk.Button(button_frame, text="保存序列", command=self.save_sequence).pack(side="left", padx=5)
        ttk.Button(button_frame, text="验证序列", command=self.validate_sequence).pack(side="left", padx=5)
        ttk.Button(button_frame, text="执行测试", command=self.execute_test).pack(side="left", padx=5)
        
        sequence_frame.columnconfigure(1, weight=1)
        sequence_frame.columnconfigure(3, weight=2)
        sequence_frame.rowconfigure(2, weight=1)
        
        self.sequence_frame = sequence_frame
        
        # 测试结果区域
        results_frame = ttk.LabelFrame(self.window, text="测试结果", padding=10)
        
        # 结果显示
        self.results_text = scrolledtext.ScrolledText(results_frame, height=10, width=80)
        self.results_text.grid(row=0, column=0, columnspan=3, sticky="nsew")
        
        # 结果操作按钮
        result_button_frame = ttk.Frame(results_frame)
        result_button_frame.grid(row=1, column=0, columnspan=3, pady=(10, 0), sticky="ew")
        
        ttk.Button(result_button_frame, text="清空结果", command=self.clear_results).pack(side="left", padx=(0, 5))
        ttk.Button(result_button_frame, text="保存结果", command=self.save_results).pack(side="left", padx=5)
        ttk.Button(result_button_frame, text="导出报告", command=self.export_report).pack(side="left", padx=5)
        
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        self.results_frame = results_frame
        
        # 状态栏
        status_frame = ttk.Frame(self.window)
        ttk.Label(status_frame, textvariable=self.status_var).pack(side="left")
        self.status_frame = status_frame
    
    def setup_layout(self):
        """设置布局"""
        self.browser_frame.pack(fill="x", padx=10, pady=(10, 5))
        self.sequence_frame.pack(fill="both", expand=True, padx=10, pady=5)
        self.results_frame.pack(fill="both", expand=True, padx=10, pady=5)
        self.status_frame.pack(fill="x", padx=10, pady=(5, 10))
    
    def load_default_sequence(self):
        """加载默认测试序列"""
        default_sequence = {
            "name": "示例Web测试",
            "description": "演示基本Web自动化操作",
            "steps": [
                {
                    "action": "goto",
                    "url": "https://httpbin.org/",
                    "description": "访问httpbin测试网站"
                },
                {
                    "action": "screenshot",
                    "filename": "homepage.png",
                    "description": "截取首页截图"
                },
                {
                    "action": "wait",
                    "timeout": 2000,
                    "description": "等待2秒"
                },
                {
                    "action": "goto",
                    "url": "https://httpbin.org/forms/post",
                    "description": "访问表单页面"
                },
                {
                    "action": "fill",
                    "selector": "input[name='custname']",
                    "text": "测试用户",
                    "description": "填写客户姓名"
                },
                {
                    "action": "screenshot",
                    "filename": "form_filled.png",
                    "description": "截取填写后的表单"
                }
            ]
        }
        
        self.sequence_text.delete(1.0, tk.END)
        self.sequence_text.insert(1.0, json.dumps(default_sequence, ensure_ascii=False, indent=2))
    
    def start_browser(self):
        """启动浏览器"""
        if not PLAYWRIGHT_AVAILABLE:
            messagebox.showerror("错误", "Playwright不可用")
            return
        
        def start_browser_thread():
            try:
                self.status_var.set("状态: 启动浏览器中...")
                
                if not self.test_manager:
                    self.test_manager = SyncPlaywrightTestManager("development")
                
                browser_name = self.browser_var.get()
                headless = self.headless_var.get()
                
                success = self.test_manager.start_browser(browser_name, headless)
                
                if success:
                    self.is_browser_running = True
                    self.status_var.set(f"状态: {browser_name}浏览器运行中")
                    self.log_result(f"✅ {browser_name}浏览器启动成功")
                else:
                    self.status_var.set("状态: 浏览器启动失败")
                    self.log_result("❌ 浏览器启动失败")
                    
            except Exception as e:
                self.status_var.set("状态: 浏览器启动异常")
                self.log_result(f"❌ 浏览器启动异常: {e}")
        
        threading.Thread(target=start_browser_thread, daemon=True).start()
    
    def close_browser(self):
        """关闭浏览器"""
        if self.test_manager and self.is_browser_running:
            try:
                self.test_manager.close_browser()
                self.is_browser_running = False
                self.status_var.set("状态: 浏览器已关闭")
                self.log_result("✅ 浏览器已关闭")
            except Exception as e:
                self.log_result(f"⚠️ 关闭浏览器异常: {e}")
    
    def validate_sequence(self):
        """验证测试序列"""
        try:
            sequence_json = self.sequence_text.get(1.0, tk.END).strip()
            sequence = json.loads(sequence_json)
            
            # 基本验证
            if not isinstance(sequence, dict):
                raise ValueError("序列必须是JSON对象")
            
            if "steps" not in sequence:
                raise ValueError("序列必须包含steps字段")
            
            if not isinstance(sequence["steps"], list):
                raise ValueError("steps必须是数组")
            
            # 验证每个步骤
            for i, step in enumerate(sequence["steps"]):
                if not isinstance(step, dict):
                    raise ValueError(f"步骤{i+1}必须是对象")
                
                if "action" not in step:
                    raise ValueError(f"步骤{i+1}缺少action字段")
            
            self.current_test_sequence = sequence
            self.log_result("✅ 测试序列验证通过")
            messagebox.showinfo("验证成功", "测试序列格式正确")
            
        except json.JSONDecodeError as e:
            error_msg = f"JSON格式错误: {e}"
            self.log_result(f"❌ {error_msg}")
            messagebox.showerror("验证失败", error_msg)
        except ValueError as e:
            error_msg = f"序列格式错误: {e}"
            self.log_result(f"❌ {error_msg}")
            messagebox.showerror("验证失败", error_msg)
        except Exception as e:
            error_msg = f"验证异常: {e}"
            self.log_result(f"❌ {error_msg}")
            messagebox.showerror("验证失败", error_msg)

    def execute_test(self):
        """执行测试"""
        if not self.is_browser_running:
            messagebox.showwarning("警告", "请先启动浏览器")
            return

        if not self.current_test_sequence:
            if not self.validate_sequence():
                return

        def execute_test_thread():
            try:
                self.status_var.set("状态: 执行测试中...")
                self.log_result(f"\n🚀 开始执行测试: {self.current_test_sequence.get('name', '未命名测试')}")

                result = self.test_manager.execute_test_sequence(self.current_test_sequence)

                if result.get("success", False):
                    self.status_var.set("状态: 测试执行完成")
                    self.log_result("✅ 测试执行成功")
                else:
                    self.status_var.set("状态: 测试执行失败")
                    self.log_result(f"❌ 测试执行失败: {result.get('error', '未知错误')}")

                # 显示详细结果
                self.display_test_result(result)
                self.test_results.append(result)

            except Exception as e:
                self.status_var.set("状态: 测试执行异常")
                self.log_result(f"❌ 测试执行异常: {e}")
                traceback.print_exc()

        threading.Thread(target=execute_test_thread, daemon=True).start()

    def display_test_result(self, result):
        """显示测试结果详情"""
        self.log_result(f"\n📊 测试结果详情:")
        self.log_result(f"   测试名称: {result.get('test_name', '未知')}")
        self.log_result(f"   开始时间: {result.get('start_time', '未知')}")
        self.log_result(f"   结束时间: {result.get('end_time', '未知')}")
        self.log_result(f"   总体结果: {'成功' if result.get('success') else '失败'}")

        steps = result.get('steps', [])
        self.log_result(f"   执行步骤: {len(steps)}个")

        for i, step in enumerate(steps):
            status = "✅" if step.get('success') else "❌"
            self.log_result(f"     {i+1}. {status} {step.get('action', '未知动作')}: {step.get('description', '无描述')}")
            if not step.get('success') and step.get('error'):
                self.log_result(f"        错误: {step['error']}")

    def load_sequence(self):
        """加载测试序列文件"""
        file_path = filedialog.askopenfilename(
            title="选择测试序列文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    sequence = json.load(f)

                self.sequence_text.delete(1.0, tk.END)
                self.sequence_text.insert(1.0, json.dumps(sequence, ensure_ascii=False, indent=2))

                if "name" in sequence:
                    self.sequence_name_var.set(sequence["name"])
                if "description" in sequence:
                    self.sequence_desc_var.set(sequence["description"])

                self.log_result(f"✅ 已加载测试序列: {file_path}")

            except Exception as e:
                error_msg = f"加载文件失败: {e}"
                self.log_result(f"❌ {error_msg}")
                messagebox.showerror("加载失败", error_msg)

    def save_sequence(self):
        """保存测试序列文件"""
        try:
            sequence_json = self.sequence_text.get(1.0, tk.END).strip()
            sequence = json.loads(sequence_json)

            # 更新名称和描述
            sequence["name"] = self.sequence_name_var.get()
            sequence["description"] = self.sequence_desc_var.get()

            file_path = filedialog.asksaveasfilename(
                title="保存测试序列",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(sequence, f, ensure_ascii=False, indent=2)

                self.log_result(f"✅ 测试序列已保存: {file_path}")

        except json.JSONDecodeError as e:
            error_msg = f"JSON格式错误: {e}"
            self.log_result(f"❌ {error_msg}")
            messagebox.showerror("保存失败", error_msg)
        except Exception as e:
            error_msg = f"保存失败: {e}"
            self.log_result(f"❌ {error_msg}")
            messagebox.showerror("保存失败", error_msg)

    def clear_results(self):
        """清空测试结果"""
        self.results_text.delete(1.0, tk.END)
        self.test_results.clear()
        self.log_result("🧹 测试结果已清空")

    def save_results(self):
        """保存测试结果"""
        if not self.test_results:
            messagebox.showinfo("提示", "没有测试结果可保存")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存测试结果",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.test_results, f, ensure_ascii=False, indent=2)

                self.log_result(f"✅ 测试结果已保存: {file_path}")

            except Exception as e:
                error_msg = f"保存结果失败: {e}"
                self.log_result(f"❌ {error_msg}")
                messagebox.showerror("保存失败", error_msg)

    def export_report(self):
        """导出HTML测试报告"""
        if not self.test_results:
            messagebox.showinfo("提示", "没有测试结果可导出")
            return

        file_path = filedialog.asksaveasfilename(
            title="导出测试报告",
            defaultextension=".html",
            filetypes=[("HTML文件", "*.html"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                self.generate_html_report(file_path)
                self.log_result(f"✅ 测试报告已导出: {file_path}")

            except Exception as e:
                error_msg = f"导出报告失败: {e}"
                self.log_result(f"❌ {error_msg}")
                messagebox.showerror("导出失败", error_msg)

    def generate_html_report(self, file_path):
        """生成HTML测试报告"""
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Playwright自动化测试报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .test-result {{ border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }}
        .success {{ border-left: 5px solid #4CAF50; }}
        .failure {{ border-left: 5px solid #f44336; }}
        .step {{ margin: 5px 0; padding: 5px; background-color: #f9f9f9; }}
        .step.success {{ background-color: #e8f5e8; }}
        .step.failure {{ background-color: #ffeaea; }}
        .timestamp {{ color: #666; font-size: 0.9em; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Playwright自动化测试报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>

    <div class="summary">
        <h2>测试摘要</h2>
        <p>总测试数: {len(self.test_results)}</p>
        <p>成功测试: {sum(1 for r in self.test_results if r.get('success'))}</p>
        <p>失败测试: {sum(1 for r in self.test_results if not r.get('success'))}</p>
    </div>

    <div class="results">
        <h2>详细结果</h2>
"""

        for i, result in enumerate(self.test_results):
            success_class = "success" if result.get('success') else "failure"
            status_text = "成功" if result.get('success') else "失败"

            html_content += f"""
        <div class="test-result {success_class}">
            <h3>测试 {i+1}: {result.get('test_name', '未命名测试')} - {status_text}</h3>
            <p class="timestamp">开始时间: {result.get('start_time', '未知')}</p>
            <p class="timestamp">结束时间: {result.get('end_time', '未知')}</p>

            <h4>执行步骤:</h4>
"""

            for step in result.get('steps', []):
                step_class = "success" if step.get('success') else "failure"
                step_status = "✅" if step.get('success') else "❌"

                html_content += f"""
            <div class="step {step_class}">
                {step_status} {step.get('action', '未知动作')}: {step.get('description', '无描述')}
"""
                if not step.get('success') and step.get('error'):
                    html_content += f"<br><strong>错误:</strong> {step['error']}"

                html_content += "</div>"

            html_content += "</div>"

        html_content += """
    </div>
</body>
</html>
"""

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

    def log_result(self, message):
        """记录结果到界面"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.results_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.results_text.see(tk.END)
        self.window.update_idletasks()

    def run(self):
        """运行界面"""
        if not self.parent:
            self.window.mainloop()


def main():
    """主函数"""
    app = PlaywrightTestUI()
    app.run()


if __name__ == "__main__":
    main()
