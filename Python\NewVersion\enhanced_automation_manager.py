#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强自动化管理器
支持新的ActionType + LogicType格式，循环控制，跳转逻辑等高级功能
"""

import sys
import os
import time
import threading
import json
import uuid
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path
import traceback

# 导入现有模块
try:
    from automation_manager import AutomationManager
    from template_image_manager import TemplateImageManager
    from image_condition_executor import ImageConditionExecutor
    from region_image_condition_executor import RegionImageConditionExecutor
    from playwright_test_manager import SyncPlaywrightTestManager, PLAYWRIGHT_AVAILABLE
except ImportError as e:
    print(f"⚠️ 导入现有模块失败: {e}")
    PLAYWRIGHT_AVAILABLE = False

class ExecutionContext:
    """执行上下文，管理变量、循环状态等"""
    
    def __init__(self):
        self.variables = {}  # 存储变量
        self.loop_stack = []  # 循环栈
        self.execution_history = []  # 执行历史
        self.current_step_index = 0  # 当前步骤索引
        self.max_iterations = 1000  # 最大迭代次数防止无限循环
        self.iteration_count = 0  # 当前迭代次数
        self.should_exit = False  # 是否应该退出执行
        self.last_result = None  # 上一步的执行结果

    def add_variable(self, name: str, value: Any):
        """添加变量"""
        self.variables[name] = value

    def get_variable(self, name: str, default=None):
        """获取变量"""
        return self.variables.get(name, default)

    def increment_variable(self, name: str, increment: int = 1):
        """递增变量"""
        current = self.get_variable(name, 0)
        self.add_variable(name, current + increment)

    def push_loop(self, loop_info: Dict):
        """压入循环信息到栈"""
        self.loop_stack.append(loop_info)

    def pop_loop(self):
        """弹出循环信息"""
        if self.loop_stack:
            return self.loop_stack.pop()
        return None

    def get_current_loop(self):
        """获取当前循环信息"""
        if self.loop_stack:
            return self.loop_stack[-1]
        return None

class EnhancedAutomationManager:
    """增强的自动化管理器 - 支持新的ActionType + LogicType格式"""

    def __init__(self, api_client=None):
        # 初始化原有的自动化管理器功能
        try:
            if api_client:
                self.base_manager = AutomationManager(api_client)
            else:
                # 创建一个模拟的API客户端或使用None
                self.base_manager = None
                print("⚠️ 未提供API客户端，将使用独立模式")
        except Exception as e:
            print(f"⚠️ 原有自动化管理器初始化失败: {e}")
            self.base_manager = None

        # 新增的组件
        try:
            if api_client:
                self.image_manager = TemplateImageManager(api_client)
            else:
                self.image_manager = None
                print("⚠️ 图像管理器将使用独立模式")
        except Exception as e:
            print(f"⚠️ 图像管理器初始化失败: {e}")
            self.image_manager = None

        self.image_condition_executor = None
        self.region_image_condition_executor = None

        # 初始化执行器
        self._init_executors()

        # 初始化Playwright测试管理器
        self.playwright_manager = None
        if PLAYWRIGHT_AVAILABLE:
            try:
                self.playwright_manager = SyncPlaywrightTestManager("development")
                print("✅ Playwright测试管理器已初始化")
            except Exception as e:
                print(f"⚠️ Playwright测试管理器初始化失败: {e}")
                self.playwright_manager = None
        else:
            print("⚠️ Playwright不可用，跳过Web自动化测试功能")

        # 执行状态
        self.is_executing = False
        self.current_execution = None

    def _init_executors(self):
        """初始化各种执行器"""
        try:
            # 尝试初始化图像条件执行器
            self.image_condition_executor = ImageConditionExecutor()
            print("✅ 图像条件执行器初始化成功")
        except Exception as e:
            print(f"⚠️ 图像条件执行器初始化失败: {e}")
            self.image_condition_executor = None

        try:
            # 尝试初始化区域图像条件执行器
            self.region_image_condition_executor = RegionImageConditionExecutor()
            print("✅ 区域图像条件执行器初始化成功")
        except Exception as e:
            print(f"⚠️ 区域图像条件执行器初始化失败: {e}")
            self.region_image_condition_executor = None

    def execute_sequence_with_logging(self, sequence_data: Dict, parameters: Dict = None) -> Dict:
        """执行序列 - 支持新格式"""
        try:
            self.is_executing = True
            self.current_execution = {
                'sequence_name': sequence_data.get('Name', '未知序列'),
                'start_time': time.time(),
                'steps_executed': 0,
                'steps_total': 0,
                'success': False,
                'error': None
            }

            sequence_name = self.current_execution['sequence_name']
            print(f"\n🚀 开始执行增强序列: {sequence_name}")
            print("=" * 60)

            # 获取步骤列表
            steps = sequence_data.get('Steps', [])
            if not steps:
                print("❌ 序列中没有步骤")
                return self._build_error_result("序列中没有步骤")

            self.current_execution['steps_total'] = len(steps)
            print(f"📋 序列包含 {len(steps)} 个步骤")
            print("=" * 60)

            # 创建执行上下文
            context = ExecutionContext()
            if parameters:
                context.variables.update(parameters)

            # 执行步骤序列
            result = self._execute_steps_with_flow_control(steps, context)

            # 更新执行结果
            self.current_execution['success'] = result.get('success', False)
            self.current_execution['steps_executed'] = context.current_step_index
            self.current_execution['end_time'] = time.time()
            self.current_execution['duration'] = self.current_execution['end_time'] - self.current_execution['start_time']

            print(f"\n" + "=" * 60)
            print(f"🎯 序列执行完成: {self.current_execution['sequence_name']}")
            print(f"   📊 执行步骤: {self.current_execution['steps_executed']}/{self.current_execution['steps_total']}")
            print(f"   ⏱️ 执行时间: {self.current_execution['duration']:.2f}秒")
            print(f"   ✅ 执行状态: {'成功' if result.get('success', False) else '失败'}")
            if context.variables:
                print(f"   🔢 最终变量: {context.variables}")
            print("=" * 60)

            return result

        except Exception as e:
            error_msg = f"序列执行失败: {str(e)}"
            print(f"❌ {error_msg}")
            traceback.print_exc()
            return self._build_error_result(error_msg)
        finally:
            self.is_executing = False

    def _execute_steps_with_flow_control(self, steps: List[Dict], context: ExecutionContext) -> Dict:
        """执行步骤序列，支持完整的流程控制"""
        
        while context.current_step_index < len(steps) and context.iteration_count < context.max_iterations:
            if context.should_exit:
                break

            step = steps[context.current_step_index]
            step_order = step.get('StepOrder', context.current_step_index + 1)
            
            print(f"\n📍 执行步骤 {step_order}: {step.get('Description', '无描述')}")

            try:
                # 执行单个步骤
                result = self._execute_single_step(step, context)
                
                # 记录执行历史
                context.execution_history.append({
                    'step_order': step_order,
                    'step': step,
                    'result': result,
                    'timestamp': time.time()
                })

                # 处理流程控制
                next_index = self._handle_flow_control(step, result, context, steps)
                
                if next_index is not None:
                    context.current_step_index = next_index
                else:
                    context.current_step_index += 1

                context.iteration_count += 1

            except Exception as e:
                error_msg = f"步骤 {step_order} 执行失败: {str(e)}"
                print(f"❌ {error_msg}")
                return self._build_error_result(error_msg)

        # 检查是否因为超过最大迭代次数而退出
        if context.iteration_count >= context.max_iterations:
            return self._build_error_result(f"执行超过最大迭代次数 ({context.max_iterations})")

        return {
            'success': True,
            'message': '序列执行完成',
            'steps_executed': len(context.execution_history),
            'execution_history': context.execution_history,
            'variables': context.variables
        }

    def _execute_single_step(self, step: Dict, context: ExecutionContext) -> Dict:
        """执行单个步骤 - 支持ActionType + LogicType组合"""

        step_order = step.get('StepOrder', 0)
        action_type = step.get('ActionType', 'click')
        logic_type = step.get('LogicType')
        parameters = step.get('Parameters', {})
        description = step.get('Description', 'No description')

        print(f"\n📍 执行步骤 {step_order}: {description}")
        print(f"   🎯 动作类型: {action_type}")
        if logic_type:
            print(f"   🧠 逻辑类型: {logic_type}")

        # 显示参数信息
        if parameters:
            print(f"   ⚙️ 参数: {parameters}")

        # 检查循环控制
        loop_count = step.get('LoopCount')
        if loop_count is not None:
            if loop_count == -1:
                print(f"   🔄 循环控制: 无限循环")
            else:
                print(f"   🔄 循环控制: {loop_count}次")

        print(f"   🔄 开始执行...")
        step_start_time = time.time()

        # 优先处理逻辑类型
        if logic_type:
            result = self._process_logic_step(step, context)
        else:
            result = self._execute_basic_action(step, context)

        # 计算执行时间
        step_duration = time.time() - step_start_time

        # 输出执行结果
        if result.get('success', False):
            print(f"   ✅ 步骤完成 (耗时: {step_duration:.2f}秒)")
            if result.get('message'):
                print(f"   💬 结果: {result['message']}")
        else:
            error_msg = result.get('error', 'Unknown error')
            print(f"   ❌ 步骤失败: {error_msg}")
            print(f"   ⏱️ 失败时间: {step_duration:.2f}秒")

        # 显示变量变化
        if hasattr(context, 'variables') and context.variables:
            print(f"   🔢 当前变量: {context.variables}")

        return result

    def _process_logic_step(self, step: Dict, context: ExecutionContext) -> Dict:
        """处理逻辑步骤"""
        logic_type = step.get('LogicType', '').lower()
        
        if logic_type == 'image_condition':
            return self._execute_image_condition_logic(step, context)
        elif logic_type == 'region_image_condition':
            return self._execute_region_image_condition_logic(step, context)
        elif logic_type == 'condition':
            return self._execute_condition_logic(step, context)
        elif logic_type == 'loop':
            return self._execute_loop_logic(step, context)
        elif logic_type == 'jump':
            return self._execute_jump_logic(step, context)
        else:
            return self._build_error_result(f"不支持的逻辑类型: {logic_type}")

    def _execute_basic_action(self, step: Dict, context: ExecutionContext) -> Dict:
        """执行基础动作"""
        action_type = step.get('ActionType', 'click').lower()
        parameters = step.get('Parameters', {})

        print(f"   执行基础动作: {action_type}")

        # 如果有原有管理器，使用它
        if self.base_manager:
            # 将新格式转换为旧格式，委托给原有的管理器
            old_format_step = {
                'actionType': action_type,
                'parameters': parameters,
                'timeoutSeconds': step.get('TimeoutSeconds', 5),
                'maxRetries': step.get('MaxRetries', 3),
                'id': step.get('StepOrder'),
                'description': step.get('Description', '')
            }

            try:
                result = self.base_manager._execute_step(old_format_step, context.variables)
                context.last_result = result
                return result
            except Exception as e:
                return self._build_error_result(f"基础动作执行失败: {str(e)}")
        else:
            # 独立模式：尝试使用Playwright执行Web相关动作
            if self.playwright_manager and action_type.lower() in ['web_goto', 'web_click', 'web_fill', 'web_screenshot', 'web_wait']:
                return self._execute_playwright_action(step, context)
            else:
                # 简单的模拟执行
                return self._simulate_basic_action(step, context)

    def _simulate_basic_action(self, step: Dict, context: ExecutionContext) -> Dict:
        """模拟基础动作执行（用于测试）"""
        action_type = step.get('ActionType', 'click').lower()
        description = step.get('Description', '无描述')

        print(f"   🎭 模拟执行: {action_type} - {description}")

        # 模拟执行时间
        import time
        time.sleep(0.5)

        # 模拟成功结果
        result = {
            'success': True,
            'action_type': action_type,
            'description': description,
            'simulated': True,
            'step_id': step.get('StepOrder'),
            'timestamp': time.time()
        }

        context.last_result = result
        return result

    def _build_error_result(self, error_message: str) -> Dict:
        """构建错误结果"""
        return {
            'success': False,
            'error': error_message,
            'timestamp': time.time()
        }

    def stop_execution(self):
        """停止执行"""
        self.is_executing = False
        if hasattr(self.base_manager, 'stop_execution'):
            self.base_manager.stop_execution()

    def get_execution_status(self) -> Dict:
        """获取执行状态"""
        return {
            'is_executing': self.is_executing,
            'current_execution': self.current_execution
        }

    # 兼容性方法 - 与原版AutomationManager保持一致
    def add_execution_callback(self, callback: Callable):
        """添加执行回调函数（兼容性方法）"""
        if not hasattr(self, 'execution_callbacks'):
            self.execution_callbacks = []
        self.execution_callbacks.append(callback)

    def remove_execution_callback(self, callback: Callable):
        """移除执行回调函数（兼容性方法）"""
        if hasattr(self, 'execution_callbacks') and callback in self.execution_callbacks:
            self.execution_callbacks.remove(callback)

    def notify_execution_status(self, status: str, message: str, data: Dict = None):
        """通知执行状态（兼容性方法）"""
        if hasattr(self, 'execution_callbacks'):
            for callback in self.execution_callbacks:
                try:
                    callback(status, message, data)
                except Exception as e:
                    print(f"⚠️ 执行回调失败: {e}")

    def get_sequences(self, refresh: bool = False) -> List[Dict]:
        """获取序列列表（兼容性方法）"""
        if self.base_manager:
            return self.base_manager.get_sequences(refresh)
        else:
            # 独立模式返回空列表
            return []

    def get_templates(self) -> List[Dict]:
        """获取模板列表（兼容性方法）"""
        if self.base_manager:
            return self.base_manager.get_templates()
        else:
            # 独立模式返回空列表
            return []

    def delete_sequence(self, sequence_id: int) -> bool:
        """删除序列（兼容性方法）"""
        if self.base_manager:
            return self.base_manager.delete_sequence(sequence_id)
        else:
            print(f"🎭 模拟删除序列: {sequence_id}")
            return True

    def execute_sequence(self, sequence_id: int, parameters: Dict = None) -> bool:
        """执行序列（兼容性方法）"""
        if self.base_manager:
            return self.base_manager.execute_sequence(sequence_id, parameters)
        else:
            # 独立模式：模拟执行
            print(f"🎭 模拟执行序列: {sequence_id}")
            return True

    def get_projects(self) -> List[Dict]:
        """获取项目列表（兼容性方法）"""
        if self.base_manager and hasattr(self.base_manager, 'get_projects'):
            return self.base_manager.get_projects()
        else:
            # 独立模式返回空列表
            return []

    def get_development_steps(self, project_id: int) -> List[Dict]:
        """获取开发步骤（兼容性方法）"""
        if self.base_manager and hasattr(self.base_manager, 'get_development_steps'):
            return self.base_manager.get_development_steps(project_id)
        else:
            # 独立模式返回空列表
            return []

    @property
    def local_components_available(self) -> bool:
        """本地组件是否可用（兼容性属性）"""
        if self.base_manager:
            return getattr(self.base_manager, 'local_components_available', True)
        else:
            return True  # 独立模式总是可用

    def _execute_image_condition_logic(self, step: Dict, context: ExecutionContext) -> Dict:
        """执行图像条件判断逻辑"""
        try:
            parameters = step.get('Parameters', {})

            print(f"   🔍 执行图像条件判断")
            print(f"      条件模板ID: {parameters.get('condition_template_id', 'N/A')}")
            print(f"      目标模板ID: {parameters.get('target_template_id', 'N/A')}")
            print(f"      反向条件: {parameters.get('reverse_condition', False)}")
            print(f"      置信度阈值: {parameters.get('condition_confidence', 0.7)}")

            if self.image_condition_executor:
                print(f"      🎯 使用真实图像检测器")
                # 使用真实的图像条件执行器
                result = self.image_condition_executor.execute_image_condition(parameters)

                # 详细输出检测结果
                if 'condition_result' in result:
                    condition_result = result['condition_result']
                    print(f"      📊 条件检测结果:")
                    print(f"         模板找到: {condition_result.get('found', False)}")
                    print(f"         置信度: {condition_result.get('confidence', 0):.3f}")
                    if condition_result.get('location'):
                        print(f"         位置: {condition_result['location']}")

                print(f"      🎯 条件满足: {result.get('condition_met', False)}")
                print(f"      🎯 动作执行: {result.get('action_executed', False)}")

            else:
                print(f"      🎭 使用模拟图像检测器")
                # 模拟图像条件判断
                result = {
                    'success': True,
                    'condition_met': True,
                    'action_executed': True,
                    'simulated': True,
                    'parameters': parameters
                }

            # 更新上下文
            context.last_result = result

            return result

        except Exception as e:
            return self._build_error_result(f"图像条件判断失败: {str(e)}")

    def _execute_region_image_condition_logic(self, step: Dict, context: ExecutionContext) -> Dict:
        """执行区域图像条件判断逻辑"""
        try:
            parameters = step.get('Parameters', {})
            print(f"   执行区域图像条件判断: {parameters}")

            if self.region_image_condition_executor:
                # 使用真实的区域图像条件执行器
                result = self.region_image_condition_executor.execute_region_image_condition(parameters)
            else:
                # 模拟区域图像条件判断
                print("   🎭 模拟区域图像条件判断")
                result = {
                    'success': True,
                    'region_found': True,
                    'target_found': True,
                    'action_executed': True,
                    'simulated': True,
                    'parameters': parameters
                }

            # 更新上下文
            context.last_result = result

            return result

        except Exception as e:
            return self._build_error_result(f"区域图像条件判断失败: {str(e)}")

    def _execute_condition_logic(self, step: Dict, context: ExecutionContext) -> Dict:
        """执行条件判断逻辑"""
        try:
            condition_expression = step.get('ConditionExpression', '')
            if not condition_expression:
                return self._build_error_result("条件表达式不能为空")

            print(f"   评估条件表达式: {condition_expression}")

            # 评估条件表达式
            result = self._evaluate_condition_expression(condition_expression, context)

            return {
                'success': True,
                'condition_result': result,
                'expression': condition_expression
            }

        except Exception as e:
            return self._build_error_result(f"条件判断失败: {str(e)}")

    def _execute_loop_logic(self, step: Dict, context: ExecutionContext) -> Dict:
        """执行循环逻辑"""
        try:
            loop_count = step.get('LoopCount')
            loop_variable = step.get('LoopVariable', '')
            group_id = step.get('GroupId', '')

            print(f"   循环控制: count={loop_count}, variable={loop_variable}, group={group_id}")

            if loop_count == -1:
                # 无限循环，需要其他条件来退出
                print("   设置无限循环")
                loop_info = {
                    'type': 'infinite',
                    'variable': loop_variable,
                    'group_id': group_id,
                    'start_step': context.current_step_index
                }
                context.push_loop(loop_info)
            elif loop_count and loop_count > 0:
                # 有限循环
                current_count = context.get_variable(loop_variable, 0)
                if current_count < loop_count:
                    context.increment_variable(loop_variable)
                    print(f"   循环计数: {current_count + 1}/{loop_count}")
                else:
                    print(f"   循环结束: {loop_count}次")

            return {'success': True, 'loop_action': 'continue'}

        except Exception as e:
            return self._build_error_result(f"循环逻辑失败: {str(e)}")

    def _execute_jump_logic(self, step: Dict, context: ExecutionContext) -> Dict:
        """执行跳转逻辑"""
        try:
            jump_to_step_id = step.get('JumpToStepId')
            if jump_to_step_id:
                print(f"   跳转到步骤: {jump_to_step_id}")
                return {
                    'success': True,
                    'jump_to': jump_to_step_id,
                    'action': 'jump'
                }
            else:
                return self._build_error_result("跳转目标步骤ID不能为空")

        except Exception as e:
            return self._build_error_result(f"跳转逻辑失败: {str(e)}")

    def _handle_flow_control(self, step: Dict, result: Dict, context: ExecutionContext, steps: List[Dict]) -> Optional[int]:
        """处理流程控制"""
        try:
            # 检查跳转
            if result.get('action') == 'jump':
                jump_to = result.get('jump_to')
                if jump_to:
                    target_index = self._find_step_index_by_order(steps, jump_to)
                    if target_index >= 0:
                        print(f"   🔄 跳转到步骤 {jump_to} (索引 {target_index})")
                        return target_index

            # 检查循环控制
            loop_count = step.get('LoopCount')
            if loop_count == -1:  # 无限循环
                # 这里需要检查退出条件
                # 暂时返回None继续下一步，实际应用中需要更复杂的逻辑
                pass
            elif loop_count and loop_count > 0:
                loop_variable = step.get('LoopVariable', '')
                current_count = context.get_variable(loop_variable, 0)
                if current_count < loop_count:
                    # 继续循环，跳回到循环开始
                    return context.current_step_index  # 重复当前步骤

            return None  # 继续下一步

        except Exception as e:
            print(f"⚠️ 流程控制处理失败: {e}")
            return None

    def _find_step_index_by_order(self, steps: List[Dict], step_order: int) -> int:
        """根据步骤顺序查找步骤索引"""
        for i, step in enumerate(steps):
            if step.get('StepOrder') == step_order:
                return i
        return -1

    def _evaluate_condition_expression(self, expression: str, context: ExecutionContext) -> bool:
        """评估条件表达式"""
        try:
            # 简单的条件表达式评估
            # 替换变量
            for var_name, var_value in context.variables.items():
                expression = expression.replace(f'{{{var_name}}}', str(var_value))

            # 安全的表达式评估（这里简化处理）
            # 实际应用中应该使用更安全的表达式解析器
            if '>' in expression:
                parts = expression.split('>')
                if len(parts) == 2:
                    left = float(parts[0].strip())
                    right = float(parts[1].strip())
                    return left > right
            elif '<' in expression:
                parts = expression.split('<')
                if len(parts) == 2:
                    left = float(parts[0].strip())
                    right = float(parts[1].strip())
                    return left < right
            elif '==' in expression:
                parts = expression.split('==')
                if len(parts) == 2:
                    left = parts[0].strip().strip('"\'')
                    right = parts[1].strip().strip('"\'')
                    return left == right

            return False

        except Exception as e:
            print(f"⚠️ 条件表达式评估失败: {e}")
            return False

    def handle_retry_loop_scenario(self, steps: List[Dict], context: ExecutionContext) -> Dict:
        """处理重试循环场景 - 专门为您的重试按钮场景设计"""
        try:
            print("🔄 开始处理重试循环场景")

            # 查找重试步骤（LoopCount = -1 的步骤）
            retry_step_index = -1
            for i, step in enumerate(steps):
                if step.get('LoopCount') == -1:
                    retry_step_index = i
                    break

            if retry_step_index == -1:
                return self._build_error_result("未找到重试循环步骤")

            retry_step = steps[retry_step_index]
            max_retry_attempts = 50  # 最大重试次数保护
            retry_count = 0

            print(f"📍 找到重试步骤: {retry_step.get('Description', '无描述')}")

            while retry_count < max_retry_attempts:
                print(f"\n🔄 重试尝试 #{retry_count + 1}")

                # 1. 先检查是否有成功标识（如果有的话）
                success_detected = self._check_success_condition(context)
                if success_detected:
                    print("✅ 检测到成功状态，退出重试循环")
                    break

                # 2. 检查是否有重试按钮
                retry_button_found = self._check_retry_button_exists(retry_step, context)

                if retry_button_found:
                    print("🔍 发现重试按钮，执行点击操作")

                    # 3. 点击重试按钮
                    click_result = self._click_retry_button(retry_step, context)

                    if click_result.get('success'):
                        print("✅ 重试按钮点击成功")

                        # 4. 等待处理
                        self._wait_for_retry_processing(retry_step)

                        retry_count += 1
                        context.add_variable('retry_count', retry_count)
                    else:
                        print("❌ 重试按钮点击失败")
                        break
                else:
                    print("ℹ️ 未发现重试按钮，可能已经成功或出现其他状态")
                    break

            if retry_count >= max_retry_attempts:
                return self._build_error_result(f"重试次数超过限制 ({max_retry_attempts})")

            return {
                'success': True,
                'retry_count': retry_count,
                'message': f'重试循环完成，共重试 {retry_count} 次'
            }

        except Exception as e:
            return self._build_error_result(f"重试循环处理失败: {str(e)}")

    def _check_success_condition(self, context: ExecutionContext) -> bool:
        """检查成功条件 - 可以根据具体需求自定义"""
        try:
            # 这里可以添加检测成功状态的逻辑
            # 例如检测特定的成功图标、文本等

            # 暂时返回False，实际使用时需要根据具体场景实现
            return False

        except Exception as e:
            print(f"⚠️ 成功条件检查失败: {e}")
            return False

    def _check_retry_button_exists(self, retry_step: Dict, context: ExecutionContext) -> bool:
        """检查重试按钮是否存在"""
        try:
            # 如果步骤有模板ID，使用图像检测
            template_id = retry_step.get('TemplateId')
            if template_id:
                # 使用图像检测重试按钮
                return self._detect_template_on_screen(template_id)

            # 如果有参数中的模板信息
            parameters = retry_step.get('Parameters', {})
            template_name = parameters.get('template_name')
            if template_name:
                return self._detect_template_by_name(template_name)

            # 默认假设重试按钮存在（实际应用中需要具体实现）
            return True

        except Exception as e:
            print(f"⚠️ 重试按钮检测失败: {e}")
            return False

    def _click_retry_button(self, retry_step: Dict, context: ExecutionContext) -> Dict:
        """点击重试按钮"""
        try:
            # 使用基础动作执行点击
            return self._execute_basic_action(retry_step, context)

        except Exception as e:
            return self._build_error_result(f"点击重试按钮失败: {str(e)}")

    def _wait_for_retry_processing(self, retry_step: Dict):
        """等待重试处理"""
        try:
            # 获取等待时间
            wait_time = retry_step.get('TimeoutSeconds', 3)
            print(f"⏱️ 等待重试处理 {wait_time} 秒...")
            time.sleep(wait_time)

        except Exception as e:
            print(f"⚠️ 等待处理失败: {e}")

    def _detect_template_on_screen(self, template_id: int) -> bool:
        """检测模板是否在屏幕上"""
        try:
            # 这里需要实现具体的图像检测逻辑
            # 暂时返回True，实际使用时需要调用图像检测功能
            return True

        except Exception as e:
            print(f"⚠️ 模板检测失败: {e}")
            return False

    def _detect_template_by_name(self, template_name: str) -> bool:
        """根据模板名称检测"""
        try:
            # 这里需要实现具体的图像检测逻辑
            # 暂时返回True，实际使用时需要调用图像检测功能
            return True

        except Exception as e:
            print(f"⚠️ 模板名称检测失败: {e}")
            return False

    def _execute_playwright_action(self, step: Dict, context: ExecutionContext) -> Dict:
        """执行Playwright Web自动化动作"""
        try:
            if not self.playwright_manager:
                return self._build_error_result("Playwright测试管理器不可用")

            action_type = step.get('ActionType', '').lower()
            parameters = step.get('Parameters', {})

            # 确保浏览器已启动
            if not hasattr(self.playwright_manager.async_manager, 'browser') or not self.playwright_manager.async_manager.browser:
                browser_started = self.playwright_manager.start_browser("chromium", headless=False)
                if not browser_started:
                    return self._build_error_result("无法启动浏览器")

            # 构建Playwright测试步骤
            playwright_step = {
                "action": action_type.replace('web_', ''),  # 移除web_前缀
                "description": step.get('Description', ''),
                "parameters": parameters
            }

            # 根据动作类型设置特定参数
            if action_type == 'web_goto':
                playwright_step["url"] = parameters.get('url', parameters.get('Url', ''))
            elif action_type == 'web_click':
                playwright_step["selector"] = parameters.get('selector', parameters.get('Selector', ''))
            elif action_type == 'web_fill':
                playwright_step["selector"] = parameters.get('selector', parameters.get('Selector', ''))
                playwright_step["text"] = parameters.get('text', parameters.get('Text', ''))
            elif action_type == 'web_screenshot':
                playwright_step["filename"] = parameters.get('filename', parameters.get('Filename', None))
            elif action_type == 'web_wait':
                playwright_step["timeout"] = parameters.get('timeout', parameters.get('Timeout', 1000))

            # 创建单步测试序列
            test_sequence = {
                "name": f"单步Web动作: {action_type}",
                "description": step.get('Description', ''),
                "steps": [playwright_step]
            }

            # 执行测试序列
            result = self.playwright_manager.execute_test_sequence(test_sequence)

            if result.get('success', False):
                return self._build_success_result(f"Web动作执行成功: {action_type}")
            else:
                error_msg = result.get('error', '未知错误')
                if result.get('steps') and len(result['steps']) > 0:
                    step_error = result['steps'][0].get('error', '')
                    if step_error:
                        error_msg = step_error
                return self._build_error_result(f"Web动作执行失败: {error_msg}")

        except Exception as e:
            return self._build_error_result(f"Playwright动作执行异常: {str(e)}")

    def start_playwright_browser(self, browser_name="chromium", headless=False) -> bool:
        """启动Playwright浏览器"""
        if not self.playwright_manager:
            print("❌ Playwright测试管理器不可用")
            return False

        try:
            result = self.playwright_manager.start_browser(browser_name, headless)
            if result:
                print(f"✅ Playwright浏览器({browser_name})启动成功")
            else:
                print(f"❌ Playwright浏览器({browser_name})启动失败")
            return result
        except Exception as e:
            print(f"❌ 启动Playwright浏览器异常: {e}")
            return False

    def close_playwright_browser(self):
        """关闭Playwright浏览器"""
        if not self.playwright_manager:
            print("❌ Playwright测试管理器不可用")
            return

        try:
            self.playwright_manager.close_browser()
            print("✅ Playwright浏览器已关闭")
        except Exception as e:
            print(f"⚠️ 关闭Playwright浏览器异常: {e}")

    def execute_web_test_sequence(self, test_sequence: Dict) -> Dict:
        """执行Web测试序列"""
        if not self.playwright_manager:
            return {"success": False, "error": "Playwright测试管理器不可用"}

        try:
            # 确保浏览器已启动
            if not hasattr(self.playwright_manager.async_manager, 'browser') or not self.playwright_manager.async_manager.browser:
                browser_started = self.playwright_manager.start_browser("chromium", headless=False)
                if not browser_started:
                    return {"success": False, "error": "无法启动浏览器"}

            result = self.playwright_manager.execute_test_sequence(test_sequence)
            return result
        except Exception as e:
            return {"success": False, "error": f"执行Web测试序列异常: {str(e)}"}

    def get_playwright_test_results(self) -> List[Dict]:
        """获取Playwright测试结果"""
        if not self.playwright_manager:
            return []

        try:
            return self.playwright_manager.get_test_results()
        except Exception as e:
            print(f"⚠️ 获取Playwright测试结果异常: {e}")
            return []

    def save_playwright_test_results(self, filename: str = None) -> str:
        """保存Playwright测试结果"""
        if not self.playwright_manager:
            return None

        try:
            return self.playwright_manager.save_test_results(filename)
        except Exception as e:
            print(f"⚠️ 保存Playwright测试结果异常: {e}")
            return None

    # 委托给base_manager的方法
    def parse_reference_images(self, reference_images: str) -> List[str]:
        """解析参考图片字符串，返回完整URL列表"""
        if self.base_manager:
            return self.base_manager.parse_reference_images(reference_images)
        else:
            print("❌ base_manager未初始化，无法解析参考图片")
            return []

    def download_reference_image(self, image_url: str) -> Optional[str]:
        """下载参考图片到本地"""
        if self.base_manager:
            return self.base_manager.download_reference_image(image_url)
        else:
            print("❌ base_manager未初始化，无法下载参考图片")
            return None

    def find_local_reference_image(self, image_url: str) -> Optional[str]:
        """查找本地已下载的参考图片"""
        if self.base_manager:
            return self.base_manager.find_local_reference_image(image_url)
        else:
            print("❌ base_manager未初始化，无法查找本地图片")
            return None

    def _execute_paste_images_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行图片粘贴动作"""
        if self.base_manager:
            return self.base_manager._execute_paste_images_action(step, parameters)
        else:
            print("❌ base_manager未初始化，无法执行图片粘贴")
            return {'success': False, 'message': 'base_manager未初始化'}

    def _execute_confirm_dialog_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行确认对话框动作"""
        if self.base_manager:
            return self.base_manager._execute_confirm_dialog_action(step, parameters)
        else:
            print("❌ base_manager未初始化，无法执行确认对话框")
            return {'success': False, 'message': 'base_manager未初始化'}

    def _execute_user_confirm_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行用户确认动作"""
        if self.base_manager:
            return self.base_manager._execute_user_confirm_action(step, parameters)
        else:
            print("❌ base_manager未初始化，无法执行用户确认")
            return {'success': False, 'message': 'base_manager未初始化'}

    def _execute_click_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行点击动作"""
        if self.base_manager:
            return self.base_manager._execute_click_action(step, parameters)
        else:
            print("❌ base_manager未初始化，无法执行点击")
            return {'success': False, 'message': 'base_manager未初始化'}

    def _execute_image_exists_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行图像存在检查动作"""
        if self.base_manager:
            return self.base_manager._execute_image_exists_action(step, parameters)
        else:
            print("❌ base_manager未初始化，无法执行图像存在检查")
            return {'success': False, 'message': 'base_manager未初始化'}

    def _execute_input_text_action(self, step: Dict, parameters: Dict) -> Dict:
        """执行输入文本动作"""
        if self.base_manager:
            return self.base_manager._execute_input_text_action(step, parameters)
        else:
            print("❌ base_manager未初始化，无法执行输入文本")
            return {'success': False, 'message': 'base_manager未初始化'}

def test_enhanced_automation_manager():
    """测试增强自动化管理器"""

    # 创建测试序列数据（基于您提供的JSON格式）
    test_sequence = {
        "Name": "增强CopilotChat代码生成流程",
        "Description": "增强CopilotChat代码生成流程",
        "Category": "CopilotChat自动化",
        "Tags": [],
        "Notes": "增强CopilotChat代码生成流程",
        "IsActive": True,
        "Steps": [
            {
                "StepOrder": 1,
                "ActionType": "wait",  # 修正为基础动作
                "LogicType": "image_condition",  # 添加逻辑类型
                "Description": "不存在则点击",
                "Parameters": {
                    "condition_template_id": 4,
                    "target_template_id": 1,
                    "target_action": "click",
                    "condition_confidence": 0.7,
                    "target_confidence": 0.7,
                    "reverse_condition": True
                },
                "TimeoutSeconds": 5,
                "MaxRetries": 3,
                "IsActive": True
            },
            {
                "StepOrder": 2,
                "ActionType": "click",
                "LogicType": None,
                "Description": "新建会话",
                "Parameters": {
                    "wait_after": 1
                },
                "TimeoutSeconds": 5,
                "MaxRetries": 3,
                "IsActive": True
            },
            {
                "StepOrder": 6,
                "ActionType": "click",
                "LogicType": None,
                "Description": "出现重试按钮，则需要点击重试按钮",
                "Parameters": {},
                "TimeoutSeconds": 5,
                "MaxRetries": 3,
                "IsActive": True,
                "LoopCount": -1,  # 无限循环
                "LoopVariable": "retry_count",
                "GroupId": "retry_loop"
            }
        ]
    }

    print("🧪 开始测试增强自动化管理器")

    try:
        # 创建管理器实例
        manager = EnhancedAutomationManager()

        # 执行测试序列
        result = manager.execute_sequence(test_sequence)

        print(f"\n📊 测试结果:")
        print(f"   成功: {result.get('success')}")
        print(f"   消息: {result.get('message', result.get('error'))}")
        print(f"   执行步骤数: {result.get('steps_executed', 0)}")

        if result.get('execution_history'):
            print(f"   执行历史: {len(result['execution_history'])} 条记录")

        return result

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # 运行测试
    test_enhanced_automation_manager()
