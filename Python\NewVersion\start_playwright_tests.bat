@echo off
chcp 65001 >nul
echo ========================================
echo    Playwright自动化测试启动脚本
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python 3.8+
    pause
    exit /b 1
)

echo ✅ Python环境正常

echo.
echo 🔧 安装和检查依赖...
python run_tests.py --install-deps
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo.
echo 🚀 启动选项:
echo 1. 打开图形界面测试工具
echo 2. 运行所有测试
echo 3. 运行Playwright测试
echo 4. 运行单元测试
echo 5. 生成测试报告
echo 6. 清理测试结果
echo 0. 退出
echo.

:menu
set /p choice="请选择操作 (0-6): "

if "%choice%"=="1" (
    echo 🖥️ 启动图形界面...
    python main.py
    goto end
)

if "%choice%"=="2" (
    echo 🧪 运行所有测试...
    python run_tests.py all -v -c
    goto show_results
)

if "%choice%"=="3" (
    echo 🌐 运行Playwright测试...
    python run_tests.py playwright -v -c
    goto show_results
)

if "%choice%"=="4" (
    echo 🔬 运行单元测试...
    python run_tests.py unit -v -c
    goto show_results
)

if "%choice%"=="5" (
    echo 📊 生成测试报告...
    if exist "test-results\html-report\report.html" (
        echo ✅ 打开HTML测试报告...
        start "" "test-results\html-report\report.html"
    ) else (
        echo ⚠️ 未找到测试报告，请先运行测试
    )
    
    if exist "test-results\coverage-html\index.html" (
        echo ✅ 打开覆盖率报告...
        start "" "test-results\coverage-html\index.html"
    )
    goto menu
)

if "%choice%"=="6" (
    echo 🧹 清理测试结果...
    python run_tests.py --clean
    echo ✅ 清理完成
    goto menu
)

if "%choice%"=="0" (
    goto end
)

echo ❌ 无效选择，请重新输入
goto menu

:show_results
echo.
echo 📊 测试完成！
if exist "test-results\html-report\report.html" (
    echo 📄 HTML报告: test-results\html-report\report.html
    set /p open_report="是否打开测试报告? (y/n): "
    if /i "%open_report%"=="y" (
        start "" "test-results\html-report\report.html"
    )
)

if exist "test-results\coverage-html\index.html" (
    echo 📈 覆盖率报告: test-results\coverage-html\index.html
    set /p open_coverage="是否打开覆盖率报告? (y/n): "
    if /i "%open_coverage%"=="y" (
        start "" "test-results\coverage-html\index.html"
    )
)

echo.
set /p continue="按任意键继续..."
goto menu

:end
echo.
echo 👋 感谢使用Playwright自动化测试工具！
pause
