#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的UI集成测试
不依赖Playwright，仅测试UI组件集成
"""

import tkinter as tk
from tkinter import ttk
import sys
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_ui_components():
    """测试UI组件"""
    print("🧪 测试UI组件集成...")
    
    try:
        # 测试导入
        print("📦 测试模块导入...")
        
        # 测试基础模块
        import tkinter
        print("✅ tkinter 导入成功")
        
        # 测试Playwright UI模块（即使Playwright不可用也应该能导入）
        try:
            from playwright_test_ui import PlaywrightTestUI
            print("✅ PlaywrightTestUI 导入成功")
            
            # 创建测试窗口
            root = tk.Tk()
            root.title("UI集成测试")
            root.geometry("600x400")
            
            # 创建选项卡
            notebook = ttk.Notebook(root)
            notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # 测试嵌入模式
            test_frame = ttk.Frame(notebook)
            notebook.add(test_frame, text="🌐 Web测试")
            
            # 创建Playwright UI（不需要实际的Playwright）
            playwright_ui = PlaywrightTestUI(parent=test_frame, automation_manager=None)
            
            print(f"✅ UI创建成功，嵌入模式: {playwright_ui.is_embedded}")
            
            # 添加信息页面
            info_frame = ttk.Frame(notebook)
            notebook.add(info_frame, text="ℹ️ 测试信息")
            
            info_label = ttk.Label(info_frame, text="""
UI集成测试成功！

✅ PlaywrightTestUI 组件已正确集成
✅ 嵌入模式工作正常
✅ 界面布局自适应

说明：
- 即使Playwright未安装，UI组件也能正常显示
- 切换到"Web测试"选项卡查看集成效果
- 实际功能需要安装Playwright才能使用

安装Playwright:
pip install playwright
playwright install
""", justify=tk.LEFT, wraplength=400)
            info_label.pack(expand=True, padx=20, pady=20)
            
            # 显示窗口
            print("🖥️ 显示测试窗口...")
            root.mainloop()
            
            return True
            
        except ImportError as e:
            print(f"❌ PlaywrightTestUI 导入失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_integration():
    """测试主程序集成"""
    print("\n🧪 测试主程序集成...")
    
    try:
        # 检查主程序文件
        main_file = Path("main.py")
        if not main_file.exists():
            print("❌ main.py 文件不存在")
            return False
        
        print("✅ main.py 文件存在")
        
        # 检查关键代码
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ("PLAYWRIGHT_UI_AVAILABLE", "Playwright可用性检查"),
            ("PlaywrightTestUI", "PlaywrightTestUI导入"),
            ("🌐 Web测试", "Web测试选项卡"),
            ("playwright_frame", "Playwright框架创建"),
        ]
        
        for check, description in checks:
            if check in content:
                print(f"✅ {description}: 已集成")
            else:
                print(f"⚠️ {description}: 未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 主程序集成检查失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 Playwright UI集成测试 (简化版)")
    print("=" * 50)
    
    # 测试1: UI组件
    test1_result = test_ui_components()
    
    # 测试2: 主程序集成
    test2_result = test_main_integration()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"   UI组件集成: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   主程序集成: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 集成测试成功！")
        print("\n💡 下一步:")
        print("   1. 安装Playwright: pip install playwright")
        print("   2. 安装浏览器: playwright install")
        print("   3. 运行主程序: python main.py")
        print("   4. 切换到'Web测试'选项卡开始使用")
    else:
        print("\n❌ 集成测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
