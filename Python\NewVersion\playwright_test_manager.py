#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Playwright测试管理器
集成到现有的自动化管理系统中，提供Web自动化测试功能
"""

import asyncio
import json
import time
import traceback
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON>erContex<PERSON>, Page
    from playwright.sync_api import sync_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    print("⚠️ Playwright未安装，请运行: pip install playwright")
    PLAYWRIGHT_AVAILABLE = False

try:
    from playwright.config import get_config, get_browser_config, ensure_directories
except ImportError:
    # 如果没有配置文件，使用默认配置
    def get_config(environment="development"):
        return {
            "base_url": "http://localhost:3000",
            "timeout": 30000,
            "action_timeout": 10000,
            "navigation_timeout": 30000,
            "browsers": [{"name": "chromium", "headless": False}],
            "test_dir": "./tests",
            "output_dir": "./test-results",
            "screenshot_dir": "./test-results/screenshots",
            "video_dir": "./test-results/videos",
            "trace_dir": "./test-results/traces",
            "screenshot": "only-on-failure",
            "video": "retain-on-failure",
            "trace": "on-first-retry",
            "retries": 2,
            "workers": 1
        }

    def get_browser_config(browser_name="chromium"):
        return {"name": browser_name, "headless": False, "viewport": {"width": 1280, "height": 720}}

    def ensure_directories():
        from pathlib import Path
        base_dir = Path(__file__).parent
        directories = [
            base_dir / "test-results",
            base_dir / "test-results/screenshots",
            base_dir / "test-results/videos"
        ]
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)


class PlaywrightTestManager:
    """Playwright测试管理器"""
    
    def __init__(self, config_env="development"):
        """初始化测试管理器"""
        if not PLAYWRIGHT_AVAILABLE:
            raise ImportError("Playwright未安装，无法使用测试功能")
            
        self.config = get_config(config_env)
        self.browser = None
        self.context = None
        self.page = None
        self.playwright = None
        
        # 测试状态
        self.is_running = False
        self.current_test = None
        self.test_results = []

        # 错误收集
        self.console_logs = []
        self.page_errors = []
        self.network_errors = []

        # 回调函数
        self.callbacks = {
            'test_started': [],
            'test_completed': [],
            'test_failed': [],
            'step_executed': [],
            'console_message': [],
            'page_error': [],
            'network_error': []
        }
        
        # 确保目录存在
        ensure_directories()
        
        print("✅ Playwright测试管理器初始化完成")
    
    def add_callback(self, event_type: str, callback: Callable):
        """添加回调函数"""
        if event_type in self.callbacks:
            self.callbacks[event_type].append(callback)
    
    def _notify_callbacks(self, event_type: str, data: Any = None):
        """通知回调函数"""
        for callback in self.callbacks.get(event_type, []):
            try:
                callback(data)
            except Exception as e:
                print(f"⚠️ 回调函数执行失败: {e}")
    
    async def start_browser(self, browser_name="chromium", headless=None):
        """启动浏览器"""
        try:
            if self.playwright is None:
                self.playwright = await async_playwright().start()
            
            browser_config = get_browser_config(browser_name)
            
            # 使用配置中的headless设置，如果没有指定的话
            if headless is None:
                headless = browser_config.get("headless", False)
            
            # 启动浏览器
            if browser_name == "chromium":
                self.browser = await self.playwright.chromium.launch(
                    headless=headless,
                    slow_mo=browser_config.get("slow_mo", 0),
                    args=browser_config.get("args", [])
                )
            elif browser_name == "firefox":
                self.browser = await self.playwright.firefox.launch(
                    headless=headless,
                    slow_mo=browser_config.get("slow_mo", 0)
                )
            elif browser_name == "webkit":
                self.browser = await self.playwright.webkit.launch(
                    headless=headless,
                    slow_mo=browser_config.get("slow_mo", 0)
                )
            else:
                raise ValueError(f"不支持的浏览器: {browser_name}")
            
            # 创建浏览器上下文
            context_options = {
                "viewport": browser_config.get("viewport", {"width": 1280, "height": 720})
            }

            # 只有在需要录制视频时才添加视频配置
            video_setting = self.config.get("video", "off")
            if video_setting != "off":
                video_dir = self.config.get("video_dir", "./test-results/videos")
                # 确保视频目录存在
                from pathlib import Path
                Path(video_dir).mkdir(parents=True, exist_ok=True)
                context_options["record_video_dir"] = video_dir
                context_options["record_video_size"] = browser_config.get("viewport", {"width": 1280, "height": 720})
            
            self.context = await self.browser.new_context(**context_options)
            
            # 创建页面
            self.page = await self.context.new_page()

            # 设置超时
            self.page.set_default_timeout(self.config["timeout"])
            self.page.set_default_navigation_timeout(self.config["navigation_timeout"])

            # 设置错误监听器
            await self._setup_error_listeners()
            
            print(f"✅ {browser_name}浏览器启动成功")
            return True
            
        except Exception as e:
            print(f"❌ 浏览器启动失败: {e}")
            traceback.print_exc()
            return False

    async def _setup_error_listeners(self):
        """设置错误监听器"""
        if not self.page:
            return

        # 监听控制台消息
        def on_console(msg):
            console_entry = {
                'timestamp': datetime.now().isoformat(),
                'type': msg.type,
                'text': msg.text,
                'location': msg.location if hasattr(msg, 'location') else None
            }
            self.console_logs.append(console_entry)

            # 如果是错误类型，特别处理
            if msg.type in ['error', 'warning']:
                print(f"🔍 控制台{msg.type}: {msg.text}")
                self._notify_callbacks('console_message', console_entry)

        self.page.on('console', on_console)

        # 监听页面错误
        def on_page_error(error):
            error_entry = {
                'timestamp': datetime.now().isoformat(),
                'message': str(error),
                'stack': getattr(error, 'stack', None)
            }
            self.page_errors.append(error_entry)
            print(f"❌ 页面错误: {error}")
            self._notify_callbacks('page_error', error_entry)

        self.page.on('pageerror', on_page_error)

        # 监听请求失败
        def on_request_failed(request):
            if request.failure:
                error_entry = {
                    'timestamp': datetime.now().isoformat(),
                    'url': request.url,
                    'method': request.method,
                    'failure': request.failure,
                    'resource_type': request.resource_type
                }
                self.network_errors.append(error_entry)
                print(f"🌐 网络错误: {request.method} {request.url} - {request.failure}")
                self._notify_callbacks('network_error', error_entry)

        self.page.on('requestfailed', on_request_failed)

        # 监听响应错误（4xx, 5xx状态码）
        def on_response(response):
            if response.status >= 400:
                error_entry = {
                    'timestamp': datetime.now().isoformat(),
                    'url': response.url,
                    'status': response.status,
                    'status_text': response.status_text,
                    'headers': dict(response.headers) if hasattr(response, 'headers') else {}
                }
                self.network_errors.append(error_entry)
                print(f"🌐 HTTP错误: {response.status} {response.url}")
                self._notify_callbacks('network_error', error_entry)

        self.page.on('response', on_response)

        print("✅ 错误监听器已设置")
    
    async def close_browser(self):
        """关闭浏览器"""
        try:
            if self.page:
                try:
                    await self.page.close()
                except Exception:
                    pass  # 忽略页面关闭错误
                self.page = None

            if self.context:
                try:
                    await self.context.close()
                except Exception:
                    pass  # 忽略上下文关闭错误
                self.context = None

            if self.browser:
                try:
                    await self.browser.close()
                except Exception:
                    pass  # 忽略浏览器关闭错误
                self.browser = None

            if self.playwright:
                try:
                    await self.playwright.stop()
                except Exception:
                    pass  # 忽略playwright停止错误
                self.playwright = None

            print("✅ 浏览器已关闭")

        except Exception as e:
            print(f"⚠️ 关闭浏览器时出错: {e}")
    
    async def navigate_to(self, url: str) -> bool:
        """导航到指定URL"""
        try:
            if not self.page:
                print("❌ 页面未初始化")
                return False
            
            await self.page.goto(url)
            print(f"✅ 导航到: {url}")
            return True
            
        except Exception as e:
            print(f"❌ 导航失败: {e}")
            return False
    
    async def take_screenshot(self, filename: str = None) -> str:
        """截图"""
        try:
            if not self.page:
                print("❌ 页面未初始化")
                return None
            
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"screenshot_{timestamp}.png"
            
            screenshot_path = Path(self.config["screenshot_dir"]) / filename
            await self.page.screenshot(path=str(screenshot_path), full_page=True)
            
            print(f"✅ 截图保存: {screenshot_path}")
            return str(screenshot_path)
            
        except Exception as e:
            print(f"❌ 截图失败: {e}")
            return None
    
    async def execute_test_sequence(self, test_sequence: Dict) -> Dict:
        """执行测试序列"""
        if not self.page:
            return {"success": False, "error": "浏览器未启动"}
        
        self.is_running = True
        self.current_test = test_sequence
        
        test_result = {
            "test_name": test_sequence.get("name", "未命名测试"),
            "start_time": datetime.now().isoformat(),
            "steps": [],
            "success": False,
            "error": None
        }
        
        try:
            self._notify_callbacks('test_started', test_sequence)
            
            steps = test_sequence.get("steps", [])
            for i, step in enumerate(steps):
                step_result = await self._execute_test_step(step, i + 1)
                test_result["steps"].append(step_result)
                
                self._notify_callbacks('step_executed', step_result)
                
                if not step_result["success"] and not step.get("continueOnError", False):
                    break
            
            # 判断整体测试是否成功
            test_result["success"] = all(step["success"] for step in test_result["steps"])
            test_result["end_time"] = datetime.now().isoformat()
            
            if test_result["success"]:
                self._notify_callbacks('test_completed', test_result)
            else:
                self._notify_callbacks('test_failed', test_result)
            
            self.test_results.append(test_result)
            return test_result
            
        except Exception as e:
            test_result["error"] = str(e)
            test_result["end_time"] = datetime.now().isoformat()
            self._notify_callbacks('test_failed', test_result)
            return test_result
        
        finally:
            self.is_running = False
            self.current_test = None
    
    async def _execute_test_step(self, step: Dict, step_number: int) -> Dict:
        """执行单个测试步骤"""
        step_result = {
            "step_number": step_number,
            "action": step.get("action", "unknown"),
            "description": step.get("description", ""),
            "success": False,
            "error": None,
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            action = step.get("action", "").lower()
            
            if action == "goto":
                url = step.get("url", step.get("parameters", {}).get("url"))
                if url:
                    await self.page.goto(url)
                    step_result["success"] = True
                else:
                    step_result["error"] = "缺少URL参数"
            
            elif action == "click":
                selector = step.get("selector", step.get("parameters", {}).get("selector"))
                if selector:
                    await self.page.click(selector)
                    step_result["success"] = True
                else:
                    step_result["error"] = "缺少选择器参数"
            
            elif action == "fill" or action == "type":
                selector = step.get("selector", step.get("parameters", {}).get("selector"))
                text = step.get("text", step.get("parameters", {}).get("text", ""))
                if selector:
                    await self.page.fill(selector, text)
                    step_result["success"] = True
                else:
                    step_result["error"] = "缺少选择器参数"
            
            elif action == "wait":
                timeout = step.get("timeout", step.get("parameters", {}).get("timeout", 1000))
                await self.page.wait_for_timeout(timeout)
                step_result["success"] = True
            
            elif action == "screenshot":
                filename = step.get("filename", step.get("parameters", {}).get("filename"))
                screenshot_path = await self.take_screenshot(filename)
                step_result["success"] = screenshot_path is not None
                step_result["screenshot_path"] = screenshot_path
            
            elif action == "assert_text":
                selector = step.get("selector", step.get("parameters", {}).get("selector"))
                expected_text = step.get("text", step.get("parameters", {}).get("text"))
                if selector and expected_text:
                    element = await self.page.wait_for_selector(selector)
                    actual_text = await element.text_content()
                    step_result["success"] = expected_text in actual_text
                    if not step_result["success"]:
                        step_result["error"] = f"期望文本'{expected_text}'，实际文本'{actual_text}'"
                else:
                    step_result["error"] = "缺少选择器或文本参数"
            
            else:
                step_result["error"] = f"不支持的动作类型: {action}"
            
        except Exception as e:
            step_result["error"] = str(e)
            step_result["success"] = False
        
        return step_result
    
    def get_test_results(self) -> List[Dict]:
        """获取测试结果"""
        return self.test_results.copy()
    
    def save_test_results(self, filename: str = None) -> str:
        """保存测试结果到文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_results_{timestamp}.json"
        
        results_path = Path(self.config["output_dir"]) / filename
        
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 测试结果已保存: {results_path}")
        return str(results_path)

    def get_console_logs(self, filter_type=None) -> List[Dict]:
        """获取控制台日志"""
        if filter_type:
            return [log for log in self.console_logs if log['type'] == filter_type]
        return self.console_logs.copy()

    def get_page_errors(self) -> List[Dict]:
        """获取页面错误"""
        return self.page_errors.copy()

    def get_network_errors(self) -> List[Dict]:
        """获取网络错误"""
        return self.network_errors.copy()

    def get_all_errors(self) -> Dict:
        """获取所有错误信息"""
        return {
            'console_errors': self.get_console_logs('error'),
            'console_warnings': self.get_console_logs('warning'),
            'page_errors': self.get_page_errors(),
            'network_errors': self.get_network_errors(),
            'total_errors': len(self.get_console_logs('error')) + len(self.page_errors) + len(self.network_errors)
        }

    def clear_errors(self):
        """清空错误记录"""
        self.console_logs.clear()
        self.page_errors.clear()
        self.network_errors.clear()
        print("🧹 错误记录已清空")

    def save_error_report(self, filename: str = None) -> str:
        """保存错误报告"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"error_report_{timestamp}.json"

        report_path = Path(self.config["output_dir"]) / filename

        error_report = {
            'timestamp': datetime.now().isoformat(),
            'summary': self.get_all_errors(),
            'detailed_logs': {
                'console_logs': self.console_logs,
                'page_errors': self.page_errors,
                'network_errors': self.network_errors
            }
        }

        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(error_report, f, ensure_ascii=False, indent=2)

        print(f"✅ 错误报告已保存: {report_path}")
        return str(report_path)


# 同步版本的简化接口
class SyncPlaywrightTestManager:
    """同步版本的Playwright测试管理器"""
    
    def __init__(self, config_env="development"):
        self.async_manager = PlaywrightTestManager(config_env)
        self.loop = None
    
    def start_browser(self, browser_name="chromium", headless=None):
        """启动浏览器（同步版本）"""
        return asyncio.run(self.async_manager.start_browser(browser_name, headless))
    
    def close_browser(self):
        """关闭浏览器（同步版本）"""
        return asyncio.run(self.async_manager.close_browser())
    
    def execute_test_sequence(self, test_sequence: Dict) -> Dict:
        """执行测试序列（同步版本）"""
        return asyncio.run(self.async_manager.execute_test_sequence(test_sequence))
    
    def take_screenshot(self, filename: str = None) -> str:
        """截图（同步版本）"""
        return asyncio.run(self.async_manager.take_screenshot(filename))
    
    def add_callback(self, event_type: str, callback: Callable):
        """添加回调函数"""
        self.async_manager.add_callback(event_type, callback)
    
    def get_test_results(self) -> List[Dict]:
        """获取测试结果"""
        return self.async_manager.get_test_results()
    
    def save_test_results(self, filename: str = None) -> str:
        """保存测试结果"""
        return self.async_manager.save_test_results(filename)

    def get_console_logs(self, filter_type=None) -> List[Dict]:
        """获取控制台日志"""
        return self.async_manager.get_console_logs(filter_type)

    def get_page_errors(self) -> List[Dict]:
        """获取页面错误"""
        return self.async_manager.get_page_errors()

    def get_network_errors(self) -> List[Dict]:
        """获取网络错误"""
        return self.async_manager.get_network_errors()

    def get_all_errors(self) -> Dict:
        """获取所有错误信息"""
        return self.async_manager.get_all_errors()

    def clear_errors(self):
        """清空错误记录"""
        self.async_manager.clear_errors()

    def save_error_report(self, filename: str = None) -> str:
        """保存错误报告"""
        return self.async_manager.save_error_report(filename)


if __name__ == "__main__":
    # 测试示例
    test_sequence = {
        "name": "基础导航测试",
        "description": "测试基本的页面导航功能",
        "steps": [
            {
                "action": "goto",
                "url": "https://www.baidu.com",
                "description": "访问百度首页"
            },
            {
                "action": "screenshot",
                "filename": "baidu_homepage.png",
                "description": "截取首页截图"
            },
            {
                "action": "wait",
                "timeout": 2000,
                "description": "等待2秒"
            }
        ]
    }
    
    # 运行测试
    manager = SyncPlaywrightTestManager()
    
    if manager.start_browser("chromium", headless=False):
        result = manager.execute_test_sequence(test_sequence)
        print(f"测试结果: {result['success']}")
        manager.save_test_results()
        manager.close_browser()
