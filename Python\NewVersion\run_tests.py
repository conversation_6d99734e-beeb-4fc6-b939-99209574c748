#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行脚本
提供便捷的测试执行和管理功能
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
import shutil


def ensure_dependencies():
    """确保测试依赖已安装"""
    print("🔍 检查测试依赖...")
    
    required_packages = [
        "pytest",
        "pytest-playwright",
        "pytest-asyncio",
        "pytest-html",
        "pytest-xdist",
        "pytest-cov",
        "playwright"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"⚠️ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("正在安装缺少的依赖...")
        
        for package in missing_packages:
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                             check=True, capture_output=True)
                print(f"✅ 已安装: {package}")
            except subprocess.CalledProcessError as e:
                print(f"❌ 安装失败: {package} - {e}")
                return False
    
    # 安装Playwright浏览器
    try:
        print("🌐 安装Playwright浏览器...")
        subprocess.run([sys.executable, "-m", "playwright", "install"], 
                      check=True, capture_output=True)
        print("✅ Playwright浏览器安装完成")
    except subprocess.CalledProcessError as e:
        print(f"⚠️ Playwright浏览器安装失败: {e}")
        print("请手动运行: playwright install")
    
    return True


def setup_test_environment():
    """设置测试环境"""
    print("🔧 设置测试环境...")
    
    # 创建测试结果目录
    test_dirs = [
        "test-results",
        "test-results/screenshots",
        "test-results/videos",
        "test-results/traces",
        "test-results/html-report",
        "test-results/coverage-html"
    ]
    
    for dir_path in test_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    print("✅ 测试环境设置完成")


def run_tests(test_type="all", verbose=False, parallel=False, coverage=False):
    """运行测试"""
    print(f"🚀 开始运行测试: {test_type}")
    
    # 基础命令
    cmd = [sys.executable, "-m", "pytest"]
    
    # 根据测试类型添加参数
    if test_type == "unit":
        cmd.extend(["-m", "not integration and not slow"])
    elif test_type == "integration":
        cmd.extend(["-m", "integration"])
    elif test_type == "slow":
        cmd.extend(["-m", "slow"])
    elif test_type == "playwright":
        cmd.extend(["-m", "playwright"])
    elif test_type == "smoke":
        cmd.extend(["-m", "smoke"])
    
    # 详细输出
    if verbose:
        cmd.append("-vv")
    
    # 并行执行
    if parallel:
        cmd.extend(["-n", "auto"])
    
    # 覆盖率报告
    if coverage:
        cmd.extend([
            "--cov=.",
            "--cov-report=html:test-results/coverage-html",
            "--cov-report=term-missing"
        ])
    
    # HTML报告
    cmd.extend([
        "--html=test-results/html-report/report.html",
        "--self-contained-html"
    ])
    
    # JUnit XML报告
    cmd.extend(["--junit-xml=test-results/junit.xml"])
    
    try:
        result = subprocess.run(cmd, cwd=Path(__file__).parent)
        
        if result.returncode == 0:
            print("✅ 测试执行成功")
            print(f"📊 测试报告: {Path('test-results/html-report/report.html').absolute()}")
            if coverage:
                print(f"📈 覆盖率报告: {Path('test-results/coverage-html/index.html').absolute()}")
        else:
            print("❌ 测试执行失败")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 测试执行异常: {e}")
        return False


def clean_test_results():
    """清理测试结果"""
    print("🧹 清理测试结果...")
    
    dirs_to_clean = [
        "test-results",
        ".pytest_cache",
        "__pycache__",
        ".coverage"
    ]
    
    for dir_path in dirs_to_clean:
        path = Path(dir_path)
        if path.exists():
            if path.is_dir():
                shutil.rmtree(path)
            else:
                path.unlink()
            print(f"   已删除: {dir_path}")
    
    print("✅ 清理完成")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Playwright自动化测试运行器")
    
    parser.add_argument(
        "test_type",
        nargs="?",
        default="all",
        choices=["all", "unit", "integration", "slow", "playwright", "smoke"],
        help="测试类型 (默认: all)"
    )
    
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="详细输出"
    )
    
    parser.add_argument(
        "-p", "--parallel",
        action="store_true",
        help="并行执行测试"
    )
    
    parser.add_argument(
        "-c", "--coverage",
        action="store_true",
        help="生成覆盖率报告"
    )
    
    parser.add_argument(
        "--setup-only",
        action="store_true",
        help="仅设置环境，不运行测试"
    )
    
    parser.add_argument(
        "--clean",
        action="store_true",
        help="清理测试结果"
    )
    
    parser.add_argument(
        "--install-deps",
        action="store_true",
        help="安装测试依赖"
    )
    
    args = parser.parse_args()
    
    # 清理测试结果
    if args.clean:
        clean_test_results()
        return
    
    # 安装依赖
    if args.install_deps:
        if not ensure_dependencies():
            sys.exit(1)
        return
    
    # 设置测试环境
    setup_test_environment()
    
    # 仅设置环境
    if args.setup_only:
        print("✅ 环境设置完成")
        return
    
    # 确保依赖已安装
    if not ensure_dependencies():
        print("❌ 依赖检查失败")
        sys.exit(1)
    
    # 运行测试
    success = run_tests(
        test_type=args.test_type,
        verbose=args.verbose,
        parallel=args.parallel,
        coverage=args.coverage
    )
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
