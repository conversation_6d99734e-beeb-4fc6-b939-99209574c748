#!/bin/bash

# Playwright自动化测试启动脚本 (Linux/Mac)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_header() {
    echo "========================================"
    print_message $BLUE "   Playwright自动化测试启动脚本"
    echo "========================================"
    echo
}

check_python() {
    print_message $YELLOW "🔍 检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        if ! command -v python &> /dev/null; then
            print_message $RED "❌ Python未安装或未添加到PATH"
            print_message $RED "请先安装Python 3.8+"
            exit 1
        else
            PYTHON_CMD="python"
        fi
    else
        PYTHON_CMD="python3"
    fi
    
    print_message $GREEN "✅ Python环境正常"
    $PYTHON_CMD --version
}

install_dependencies() {
    print_message $YELLOW "🔧 安装和检查依赖..."
    
    if ! $PYTHON_CMD run_tests.py --install-deps; then
        print_message $RED "❌ 依赖安装失败"
        exit 1
    fi
    
    print_message $GREEN "✅ 依赖检查完成"
}

show_menu() {
    echo
    print_message $BLUE "🚀 启动选项:"
    echo "1. 打开图形界面测试工具"
    echo "2. 运行所有测试"
    echo "3. 运行Playwright测试"
    echo "4. 运行单元测试"
    echo "5. 生成测试报告"
    echo "6. 清理测试结果"
    echo "0. 退出"
    echo
}

open_report() {
    local report_path=$1
    local report_name=$2
    
    if [[ -f "$report_path" ]]; then
        print_message $GREEN "✅ 打开${report_name}..."
        
        # 检测操作系统并使用相应的命令打开文件
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            open "$report_path"
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            # Linux
            if command -v xdg-open &> /dev/null; then
                xdg-open "$report_path"
            elif command -v firefox &> /dev/null; then
                firefox "$report_path" &
            elif command -v google-chrome &> /dev/null; then
                google-chrome "$report_path" &
            else
                print_message $YELLOW "⚠️ 无法自动打开浏览器，请手动打开: $report_path"
            fi
        else
            print_message $YELLOW "⚠️ 不支持的操作系统，请手动打开: $report_path"
        fi
    else
        print_message $YELLOW "⚠️ 未找到${report_name}，请先运行测试"
    fi
}

show_results() {
    echo
    print_message $GREEN "📊 测试完成！"
    
    if [[ -f "test-results/html-report/report.html" ]]; then
        echo "📄 HTML报告: test-results/html-report/report.html"
        read -p "是否打开测试报告? (y/n): " open_report_choice
        if [[ "$open_report_choice" =~ ^[Yy]$ ]]; then
            open_report "test-results/html-report/report.html" "HTML测试报告"
        fi
    fi
    
    if [[ -f "test-results/coverage-html/index.html" ]]; then
        echo "📈 覆盖率报告: test-results/coverage-html/index.html"
        read -p "是否打开覆盖率报告? (y/n): " open_coverage_choice
        if [[ "$open_coverage_choice" =~ ^[Yy]$ ]]; then
            open_report "test-results/coverage-html/index.html" "覆盖率报告"
        fi
    fi
    
    echo
    read -p "按Enter键继续..."
}

main() {
    # 切换到脚本所在目录
    cd "$(dirname "$0")"
    
    print_header
    check_python
    install_dependencies
    
    while true; do
        show_menu
        read -p "请选择操作 (0-6): " choice
        
        case $choice in
            1)
                print_message $BLUE "🖥️ 启动图形界面..."
                $PYTHON_CMD main.py
                break
                ;;
            2)
                print_message $BLUE "🧪 运行所有测试..."
                $PYTHON_CMD run_tests.py all -v -c
                show_results
                ;;
            3)
                print_message $BLUE "🌐 运行Playwright测试..."
                $PYTHON_CMD run_tests.py playwright -v -c
                show_results
                ;;
            4)
                print_message $BLUE "🔬 运行单元测试..."
                $PYTHON_CMD run_tests.py unit -v -c
                show_results
                ;;
            5)
                print_message $BLUE "📊 生成测试报告..."
                open_report "test-results/html-report/report.html" "HTML测试报告"
                open_report "test-results/coverage-html/index.html" "覆盖率报告"
                ;;
            6)
                print_message $BLUE "🧹 清理测试结果..."
                $PYTHON_CMD run_tests.py --clean
                print_message $GREEN "✅ 清理完成"
                ;;
            0)
                break
                ;;
            *)
                print_message $RED "❌ 无效选择，请重新输入"
                ;;
        esac
    done
    
    echo
    print_message $GREEN "👋 感谢使用Playwright自动化测试工具！"
}

# 运行主函数
main "$@"
