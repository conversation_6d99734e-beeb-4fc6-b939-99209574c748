#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
线程安全测试脚本
测试UI组件的线程安全修复是否有效
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import sys
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_thread_safety():
    """测试线程安全"""
    print("🧪 开始线程安全测试...")
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.title("线程安全测试")
        root.geometry("600x400")
        
        # 创建状态标签
        status_label = ttk.Label(root, text="初始状态", font=('Arial', 12))
        status_label.pack(pady=20)
        
        # 创建测试按钮框架
        button_frame = ttk.Frame(root)
        button_frame.pack(pady=20)
        
        # 测试结果显示
        result_text = tk.Text(root, height=15, width=70)
        result_text.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        def log_message(message):
            """记录消息"""
            timestamp = time.strftime("%H:%M:%S")
            result_text.insert(tk.END, f"[{timestamp}] {message}\n")
            result_text.see(tk.END)
            root.update_idletasks()
        
        def safe_update_status(message):
            """线程安全的状态更新"""
            try:
                if threading.current_thread() is threading.main_thread():
                    # 主线程直接更新
                    status_label.config(text=message)
                    log_message(f"主线程更新: {message}")
                else:
                    # 子线程使用after调度
                    root.after(0, lambda: status_label.config(text=message))
                    root.after(0, lambda: log_message(f"子线程更新: {message}"))
            except Exception as e:
                print(f"状态更新失败: {e}")
        
        def test_main_thread_update():
            """测试主线程更新"""
            safe_update_status("主线程测试")
            log_message("✅ 主线程更新测试完成")
        
        def test_background_thread_update():
            """测试后台线程更新"""
            def background_task():
                time.sleep(1)  # 模拟耗时操作
                safe_update_status("后台线程测试")
                root.after(0, lambda: log_message("✅ 后台线程更新测试完成"))
            
            threading.Thread(target=background_task, daemon=True).start()
            log_message("🚀 启动后台线程测试...")
        
        def test_multiple_threads():
            """测试多线程更新"""
            def worker(thread_id):
                for i in range(3):
                    time.sleep(0.5)
                    safe_update_status(f"线程{thread_id}-步骤{i+1}")
                    root.after(0, lambda tid=thread_id, step=i+1: 
                              log_message(f"线程{tid}完成步骤{step}"))
            
            # 启动多个线程
            for i in range(3):
                threading.Thread(target=worker, args=(i+1,), daemon=True).start()
            
            log_message("🚀 启动多线程测试...")
        
        def simulate_project_load():
            """模拟项目加载过程"""
            def load_simulation():
                try:
                    safe_update_status("加载项目中...")
                    root.after(0, lambda: log_message("开始模拟项目加载"))
                    
                    time.sleep(2)  # 模拟API调用
                    
                    safe_update_status("✅ 项目加载完成")
                    root.after(0, lambda: log_message("✅ 项目加载模拟完成"))
                    
                except Exception as e:
                    safe_update_status(f"❌ 加载失败: {e}")
                    root.after(0, lambda: log_message(f"❌ 项目加载模拟失败: {e}"))
            
            threading.Thread(target=load_simulation, daemon=True).start()
            log_message("🚀 启动项目加载模拟...")
        
        # 创建测试按钮
        ttk.Button(button_frame, text="主线程更新", 
                  command=test_main_thread_update).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="后台线程更新", 
                  command=test_background_thread_update).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="多线程更新", 
                  command=test_multiple_threads).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="模拟项目加载", 
                  command=simulate_project_load).pack(side=tk.LEFT, padx=5)
        
        # 清空按钮
        def clear_log():
            result_text.delete(1.0, tk.END)
            status_label.config(text="日志已清空")
        
        ttk.Button(button_frame, text="清空日志", 
                  command=clear_log).pack(side=tk.LEFT, padx=5)
        
        # 初始化日志
        log_message("🎯 线程安全测试界面已启动")
        log_message("💡 点击按钮测试不同的线程更新场景")
        log_message("📝 观察状态标签的更新和错误信息")
        
        # 运行主循环
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_project_manager_import():
    """测试项目管理器导入"""
    print("🧪 测试项目管理器导入...")
    
    try:
        from ui_components.project_task_manager import ProjectTaskManager
        print("✅ ProjectTaskManager 导入成功")
        
        # 检查是否有线程安全方法
        manager = ProjectTaskManager(None, None, None, None, None, None)
        
        if hasattr(manager, '_safe_ui_update'):
            print("✅ _safe_ui_update 方法存在")
        else:
            print("⚠️ _safe_ui_update 方法不存在")
        
        if hasattr(manager, '_is_main_thread'):
            print("✅ _is_main_thread 方法存在")
        else:
            print("⚠️ _is_main_thread 方法不存在")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 线程安全修复测试")
    print("=" * 50)
    
    # 测试1: 导入测试
    test1_result = test_project_manager_import()
    
    print("\n" + "=" * 50)
    
    if test1_result:
        print("📊 导入测试通过，启动UI测试...")
        # 测试2: UI测试
        test2_result = test_thread_safety()
    else:
        print("❌ 导入测试失败，跳过UI测试")
        test2_result = False
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"   导入测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   UI测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 线程安全修复测试成功！")
        print("💡 现在可以安全地在多线程环境中使用UI组件了")
    else:
        print("\n❌ 测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
