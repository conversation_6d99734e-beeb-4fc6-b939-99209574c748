#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Playwright测试配置文件
用于配置自动化测试的各种参数和设置
"""

import os
from pathlib import Path

# 测试配置
PLAYWRIGHT_CONFIG = {
    # 基础配置
    "base_url": "http://localhost:3000",
    "timeout": 30000,  # 30秒超时
    "action_timeout": 10000,  # 10秒动作超时
    "navigation_timeout": 30000,  # 30秒导航超时
    
    # 浏览器配置
    "browsers": [
        {
            "name": "chromium",
            "headless": False,  # 显示浏览器窗口
            "slow_mo": 100,  # 每个操作间隔100ms
            "viewport": {"width": 1280, "height": 720},
            "args": [
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--no-sandbox"
            ]
        },
        {
            "name": "firefox",
            "headless": False,
            "slow_mo": 100,
            "viewport": {"width": 1280, "height": 720}
        },
        {
            "name": "webkit",
            "headless": False,
            "slow_mo": 100,
            "viewport": {"width": 1280, "height": 720}
        }
    ],
    
    # 测试目录配置
    "test_dir": "./tests",
    "output_dir": "./test-results",
    "screenshot_dir": "./test-results/screenshots",
    "video_dir": "./test-results/videos",
    "trace_dir": "./test-results/traces",
    
    # 报告配置
    "reporters": [
        {"type": "html", "output_dir": "./test-results/html-report"},
        {"type": "json", "output_file": "./test-results/results.json"},
        {"type": "junit", "output_file": "./test-results/junit.xml"},
        {"type": "line"}
    ],
    
    # 重试配置
    "retries": 2,
    "workers": 1,  # 并行工作进程数
    
    # 截图和视频配置
    "screenshot": "only-on-failure",  # 仅失败时截图
    "video": "retain-on-failure",     # 仅失败时保留视频
    "trace": "on-first-retry",        # 首次重试时记录trace
    
    # 全局设置
    "fully_parallel": False,  # 不完全并行执行
    "forbid_only": False,     # 允许使用test.only
    
    # 移动设备配置
    "mobile_devices": [
        {
            "name": "iPhone 12",
            "viewport": {"width": 390, "height": 844},
            "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15"
        },
        {
            "name": "Pixel 5",
            "viewport": {"width": 393, "height": 851},
            "user_agent": "Mozilla/5.0 (Linux; Android 11; Pixel 5) AppleWebKit/537.36"
        }
    ]
}

# 环境配置
ENV_CONFIG = {
    "development": {
        "base_url": "http://localhost:3000",
        "api_url": "http://localhost:5000/api",
        "headless": False,
        "slow_mo": 100
    },
    "staging": {
        "base_url": "https://staging.example.com",
        "api_url": "https://staging-api.example.com/api",
        "headless": True,
        "slow_mo": 0
    },
    "production": {
        "base_url": "https://production.example.com",
        "api_url": "https://api.example.com/api",
        "headless": True,
        "slow_mo": 0
    }
}

def get_config(environment="development"):
    """获取指定环境的配置"""
    config = PLAYWRIGHT_CONFIG.copy()
    env_config = ENV_CONFIG.get(environment, ENV_CONFIG["development"])
    
    # 合并环境特定配置
    config.update(env_config)
    
    return config

def get_browser_config(browser_name="chromium"):
    """获取指定浏览器的配置"""
    for browser in PLAYWRIGHT_CONFIG["browsers"]:
        if browser["name"] == browser_name:
            return browser
    return PLAYWRIGHT_CONFIG["browsers"][0]  # 默认返回第一个

def ensure_directories():
    """确保测试相关目录存在"""
    base_dir = Path(__file__).parent
    
    directories = [
        base_dir / "tests",
        base_dir / "test-results",
        base_dir / "test-results/screenshots",
        base_dir / "test-results/videos",
        base_dir / "test-results/traces",
        base_dir / "test-results/html-report"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        
    print("✅ 测试目录结构已创建")

if __name__ == "__main__":
    # 创建测试目录结构
    ensure_directories()
    
    # 打印配置信息
    config = get_config()
    print("🔧 Playwright配置信息:")
    print(f"   基础URL: {config['base_url']}")
    print(f"   测试目录: {config['test_dir']}")
    print(f"   输出目录: {config['output_dir']}")
    print(f"   浏览器数量: {len(config['browsers'])}")
    print(f"   重试次数: {config['retries']}")
