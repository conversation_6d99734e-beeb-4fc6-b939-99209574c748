#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pytest配置文件
定义测试夹具和全局配置
"""

import pytest
import asyncio
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from playwright_test_manager import PlaywrightTestManager, SyncPlaywrightTestManager


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def playwright_manager():
    """创建Playwright测试管理器"""
    manager = PlaywrightTestManager("development")
    
    # 启动浏览器
    await manager.start_browser("chromium", headless=True)
    
    yield manager
    
    # 清理
    await manager.close_browser()


@pytest.fixture(scope="session")
def sync_playwright_manager():
    """创建同步版本的Playwright测试管理器"""
    manager = SyncPlaywrightTestManager("development")
    
    # 启动浏览器
    manager.start_browser("chromium", headless=True)
    
    yield manager
    
    # 清理
    manager.close_browser()


@pytest.fixture
def sample_test_sequence():
    """示例测试序列"""
    return {
        "name": "示例测试序列",
        "description": "用于测试的示例序列",
        "steps": [
            {
                "action": "goto",
                "url": "https://httpbin.org/",
                "description": "访问httpbin测试网站"
            },
            {
                "action": "screenshot",
                "filename": "httpbin_homepage.png",
                "description": "截取首页截图"
            },
            {
                "action": "wait",
                "timeout": 1000,
                "description": "等待1秒"
            }
        ]
    }


@pytest.fixture
def complex_test_sequence():
    """复杂测试序列"""
    return {
        "name": "复杂测试序列",
        "description": "包含多种操作的复杂测试序列",
        "steps": [
            {
                "action": "goto",
                "url": "https://httpbin.org/forms/post",
                "description": "访问表单页面"
            },
            {
                "action": "fill",
                "selector": "input[name='custname']",
                "text": "测试用户",
                "description": "填写客户姓名"
            },
            {
                "action": "fill",
                "selector": "input[name='custtel']",
                "text": "13800138000",
                "description": "填写电话号码"
            },
            {
                "action": "fill",
                "selector": "input[name='custemail']",
                "text": "<EMAIL>",
                "description": "填写邮箱地址"
            },
            {
                "action": "screenshot",
                "filename": "form_filled.png",
                "description": "截取填写后的表单"
            },
            {
                "action": "click",
                "selector": "input[type='submit']",
                "description": "提交表单"
            },
            {
                "action": "wait",
                "timeout": 2000,
                "description": "等待页面加载"
            },
            {
                "action": "screenshot",
                "filename": "form_submitted.png",
                "description": "截取提交后的页面"
            }
        ]
    }


# 测试配置
def pytest_configure(config):
    """Pytest配置"""
    # 添加自定义标记
    config.addinivalue_line(
        "markers", "slow: 标记测试为慢速测试"
    )
    config.addinivalue_line(
        "markers", "integration: 标记测试为集成测试"
    )
    config.addinivalue_line(
        "markers", "ui: 标记测试为UI测试"
    )


def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    # 为所有异步测试添加asyncio标记
    for item in items:
        if asyncio.iscoroutinefunction(item.function):
            item.add_marker(pytest.mark.asyncio)


# 测试报告钩子
def pytest_html_report_title(report):
    """自定义HTML报告标题"""
    report.title = "Playwright自动化测试报告"


def pytest_html_results_summary(prefix, summary, postfix):
    """自定义HTML报告摘要"""
    prefix.extend([
        "<h2>测试环境信息</h2>",
        "<p>浏览器: Chromium (Headless)</p>",
        "<p>测试框架: Playwright + Pytest</p>"
    ])
