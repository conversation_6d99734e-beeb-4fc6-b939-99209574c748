#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试同步版本修复
"""

from playwright_test_manager import SyncPlaywrightTestManager

def test_sync_manager():
    """测试同步管理器"""
    print("🧪 测试新的同步管理器...")
    
    try:
        print("1. 创建管理器...")
        manager = SyncPlaywrightTestManager()
        
        print("2. 启动浏览器...")
        result = manager.start_browser('chromium', headless=False)
        print(f"   浏览器启动结果: {result}")
        
        if result:
            print("3. 执行测试...")
            test_sequence = {
                "name": "测试百度访问",
                "steps": [
                    {
                        "action": "goto",
                        "url": "https://www.baidu.com",
                        "description": "访问百度首页"
                    },
                    {
                        "action": "screenshot",
                        "filename": "baidu_test.png",
                        "description": "截取百度首页"
                    }
                ]
            }
            
            test_result = manager.execute_test_sequence(test_sequence)
            print(f"   测试执行结果: {test_result.get('success', False)}")
            
            if test_result.get('steps'):
                for i, step in enumerate(test_result['steps']):
                    status = "✅" if step.get('success') else "❌"
                    print(f"   步骤{i+1}: {status} {step.get('action')} - {step.get('description')}")
                    if not step.get('success') and step.get('error'):
                        print(f"          错误: {step['error']}")
            
            print("4. 关闭浏览器...")
            manager.close_browser()
            
            return test_result.get('success', False)
        else:
            print("❌ 浏览器启动失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🔧 同步Playwright管理器修复测试")
    print("=" * 50)
    
    success = test_sync_manager()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 同步管理器修复成功！")
        print("💡 现在可以在UI中正常使用Playwright功能了")
    else:
        print("❌ 同步管理器修复失败")
        print("💡 请检查错误信息并重试")
