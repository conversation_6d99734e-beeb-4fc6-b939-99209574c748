#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON文本框读取功能
"""

import tkinter as tk
from tkinter import ttk
import json
import sys
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from playwright_test_ui import PlaywrightTestUI

def test_json_reading():
    """测试JSON文本框读取功能"""
    print("🧪 测试JSON文本框读取功能...")
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.title("JSON读取测试")
        root.geometry("800x600")
        
        # 创建测试框架
        test_frame = ttk.Frame(root)
        test_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建Playwright UI
        playwright_ui = PlaywrightTestUI(parent=test_frame, automation_manager=None)
        
        # 测试JSON序列
        test_json = {
            "name": "JSON读取测试",
            "description": "测试从文本框读取JSON内容",
            "steps": [
                {
                    "action": "goto",
                    "url": "https://www.baidu.com",
                    "description": "访问百度首页"
                },
                {
                    "action": "screenshot",
                    "filename": "json_test.png",
                    "description": "截图测试"
                }
            ]
        }
        
        # 将测试JSON放入文本框
        playwright_ui.sequence_text.delete(1.0, tk.END)
        playwright_ui.sequence_text.insert(1.0, json.dumps(test_json, ensure_ascii=False, indent=2))
        
        # 创建测试按钮
        button_frame = ttk.Frame(test_frame)
        button_frame.pack(pady=10)
        
        def test_validate():
            """测试验证功能"""
            result = playwright_ui.validate_sequence()
            print(f"验证结果: {result}")
            if result and playwright_ui.current_test_sequence:
                print(f"解析的序列名称: {playwright_ui.current_test_sequence.get('name')}")
                print(f"步骤数量: {len(playwright_ui.current_test_sequence.get('steps', []))}")
        
        def test_execute_parsing():
            """测试执行时的解析功能"""
            # 模拟浏览器运行状态
            playwright_ui.is_browser_running = True
            
            # 清空当前序列，强制重新解析
            playwright_ui.current_test_sequence = None
            
            # 模拟执行测试的解析部分
            try:
                sequence_json = playwright_ui.sequence_text.get(1.0, tk.END).strip()
                print(f"从文本框读取的JSON长度: {len(sequence_json)}")
                
                if sequence_json:
                    parsed_sequence = json.loads(sequence_json)
                    print(f"解析成功！序列名称: {parsed_sequence.get('name')}")
                    print(f"步骤数量: {len(parsed_sequence.get('steps', []))}")
                    
                    # 显示第一个步骤
                    if parsed_sequence.get('steps'):
                        first_step = parsed_sequence['steps'][0]
                        print(f"第一个步骤: {first_step.get('action')} - {first_step.get('url')}")
                else:
                    print("❌ 文本框为空")
                    
            except Exception as e:
                print(f"❌ 解析失败: {e}")
        
        def modify_json():
            """修改JSON内容测试"""
            modified_json = test_json.copy()
            modified_json["name"] = "修改后的测试序列"
            modified_json["steps"][0]["url"] = "https://www.google.com"
            
            playwright_ui.sequence_text.delete(1.0, tk.END)
            playwright_ui.sequence_text.insert(1.0, json.dumps(modified_json, ensure_ascii=False, indent=2))
            
            print("✅ JSON内容已修改")
        
        ttk.Button(button_frame, text="测试验证", command=test_validate).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="测试执行解析", command=test_execute_parsing).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="修改JSON", command=modify_json).pack(side=tk.LEFT, padx=5)
        
        # 添加说明
        info_label = ttk.Label(test_frame, text="""
测试说明:
1. 点击"测试验证"按钮测试validate_sequence功能
2. 点击"测试执行解析"按钮测试execute_test中的JSON解析
3. 点击"修改JSON"按钮修改文本框内容，然后重新测试
4. 观察控制台输出查看解析结果
""", justify=tk.LEFT)
        info_label.pack(pady=10)
        
        print("✅ 测试界面已创建")
        print("💡 请在界面中测试JSON读取功能")
        
        # 运行界面
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🔧 JSON文本框读取功能测试")
    print("=" * 50)
    
    test_json_reading()
